package org.springblade.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 车场信息表实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@TableName("d_parklot")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Parklot对象", description = "车场信息表")
public class Parklot extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 设置id为手动设置，取之前的最大值+1
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@TableId(type = IdType.INPUT)
	private Long id;

	/**
	 * 名称
	 */
	@NotEmpty(message = "车场名称不能为空")
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 全称
	 */
	@ApiModelProperty(value = "全称")
	private String fullName;
	/**
	 * 备注，描述
	 */
	@ApiModelProperty(value = "备注，描述")
	private String memo;
	/**
	 * 是否运行临停车 识别自动开杆进场 1运行 0不允许
	 */
	@ApiModelProperty(value = "是否运行临停车 识别自动开杆进场 1运行 0不允许")
	@NotNull(message = "识别自动开杆进场不能为空")
	private Boolean autoEnter;
	/**
	 * 是否开启临停时段限制
	 */
	@ApiModelProperty(value = "是否开启临停时段限制")
	@NotNull(message = "是否开启临停时段限制不能为空")
	private Boolean limitAutoTime;
	/**
	 * 临停车限制开始时间 单位分
	 */
	@ApiModelProperty(value = "临停车限制开始时间 单位分")
	private Integer limitAutoBeginTime;
	/**
	 * 临停车限制结束时间 单位分
	 */
	@ApiModelProperty(value = "临停车限制结束时间 单位分")
	private Integer limitAutoEndTime;
	/**
	 * 是否运行临停车 识别自动开杆出场
	 */
	@ApiModelProperty(value = "是否运行临停车 识别自动开杆出场")
	@NotNull(message = "识别自动开杆出场不能为空")
	private Boolean autoExit;
	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private BigDecimal lng;
	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private BigDecimal lat;
	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址")
	private String address;
	/**
	 * 车位数
	 */
	@ApiModelProperty(value = "车位数")
	private Integer lotAmount;
	/**
	 * 总层数
	 */
	@ApiModelProperty(value = "总层数")
	private Integer floorAmount;

	/**
	 * 独立产权车位数
	 */
	@ApiModelProperty(value = "独立产权车位数")
	private Integer independentOwnershipAmount;

	/**
	 * 上浮率
	 */
	@ApiModelProperty(value = "上浮率")
	private Integer floatingRatio;

	/**
	 * 临停车位数
	 */
	@ApiModelProperty(value = "临停车位数")
	private Integer tempLotAmount;

	/**
	 * vip车位
	 */
	@ApiModelProperty(value = "vip车位")
	private Integer vipLotAmount;

	/**
	 * 无障碍车位数
	 */
	@ApiModelProperty(value = "无障碍车位数")
	private Integer accessibilityLotAmount;

	/**
	 * 已售（开卡)子母车位数
	 */
	@ApiModelProperty(value = "已售（开卡)子母车位数")
	private Integer soldLetterLotAmount;

	/**
	 * 已售车位数
	 */
	@ApiModelProperty(value = "已售车位数")
	private Integer soldLotAmount;
	/**
	 * 已租车位数
	 */
	@ApiModelProperty(value = "已租车位数")
	private Integer lettingLotAmount;
	/**
	 * 字母车位数
	 */
	@ApiModelProperty(value = "字母车位数")
	private Integer letterLotAmount;

	/**
	 * 车场父id（场中场使用）
	 */
	@ApiModelProperty(value = "车场父id（场中场使用）")
	private Long parentId;

	/**
	 * 是否启用地杆二次进场校验
	 */
	@ApiModelProperty(value = "是否启用地杆二次进场校验")
	private Boolean isSecondEnterConfirm;

	/**
	 * 总面积
	 */
	@ApiModelProperty(value = "总面积")
	private BigDecimal totalAreaAmount;
	/**
	 * 修改车牌次数限制规则
	 */
	@ApiModelProperty(value = "修改车牌次数限制规则")
	private String plateChangeRules;
	@ApiModelProperty(value = "一天内允许改变次数")
	private Integer dayChangeRule;
	@ApiModelProperty(value = "一周内允许改变次数")
	private Integer weekChangeRule;
	@ApiModelProperty(value = "一月内允许改变次数")
	private Integer monthChangeRule;
	@ApiModelProperty(value = "车场编号")
	private String parklotNo;
	@ApiModelProperty(value = "月卡类型")
	private String cardTypes;
	@ApiModelProperty(value = "已租的无障碍车位")
	private Integer rentAccLotAmount;
	@ApiModelProperty(value = "车场类型（1-路外2-路内）")
	private Integer parklotType;

	@ApiModelProperty(value = "访客用户每月授权限制")
	private Integer visitorUserLimit;

	@ApiModelProperty(value = "访客车场每月授权限制")
	private Integer visitorParklotLimit;

	@ApiModelProperty(value = "是否嵌套车场(场中场)")
	private Boolean isNest;


	@ApiModelProperty(value = "是否实时计算车位")
	private Boolean realTimeCalculate;

	@ApiModelProperty(value = "是否支持ETC扣费")
	@TableField("etc_support")
	private Boolean ETCSupport;

	@ApiModelProperty(value = "是否支持无感支付[false-不支持,true-支持]")
	private Boolean noFeelSupport;


	@ApiModelProperty(value = "续费方式  1，按月续费 2 按天续费")
	private Integer payStyle;


	@TableField(exist = false)
	private Double distance;


	@ApiModelProperty(value = "数据是否加水印")
	private Boolean watermark;

	@ApiModelProperty(value = "是否模糊显示车位数")
	private Boolean fuzzyDisplay;

}
