package org.springblade.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.CorpseCleanLogDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.dto.ParkingOrderRemainPlaceVO;
import com.lecent.park.dto.TotalStatisticsDTO;
import com.lecent.park.entity.ParkingOrder;
import com.lecent.park.vo.*;
import org.springblade.core.mp.base.BaseService;
import org.springblade.report.vo.ParkingOrderStatistics;
import org.springblade.report.vo.ParkingOrderUserVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 停车订单服务类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
public interface IParkingOrderService extends BaseService<ParkingOrder> {

	/**
	 * 查询在场车辆
	 * <p>
	 * 近7天流量分析
	 *
	 * @param parklotId 车场id
	 * @return List<ParkingOrderStatisticsVO>
	 */
	ParkLastWeekVO lastWeekParkingOrder(String parklotId);

	/**
	 * 获取今天的总收入
	 *
	 * @param parklotIds
	 * @return String
	 */
	BigDecimal getTodayIncome(List<Long> parklotIds);

	/**
	 * 今日欠费总金额
	 *
	 * @param parklotIds
	 * @return String
	 */
	BigDecimal getTodayOwnFee(List<Long> parklotIds);

	/**
	 * 当日停车时段分析
	 *
	 * @param parklotId 车场id
	 * @return
	 */
	List<StopDurationVO> stopDuration(List<Long> parklotId);

	int getNumInPark(List<Long> parklotIds, Date date);

	/**
	 * 泊位利用率分析
	 *
	 * @param parklotId 车场id 车场id
	 * @return ParkLastWeekRateVO
	 */
	ParkLastWeekRateVO parkUseRate(String parklotId);

	/**
	 * 财务分析
	 *
	 * @param parklotId 车场id
	 * @return 财务分析
	 */
	List<List<String>> financialAnalysis(String parklotId);

	/**
	 * 在场车位数
	 *
	 * @param parklotIds 车场id
	 * @return
	 */
	Integer getTodayParkingSum(List<Long> parklotIds);

	/**
	 * 今天过车数量
	 *
	 * @param parklotId 车场id列表
	 * @return
	 */
	Integer getTodayPassNum(List<Long> parklotId);

	/**
	 * 统计今日临停收费总和、今日异常少收金额、当日过车数量
	 *
	 * @param parklotIds
	 * @return
	 */
	TotalStatisticsDTO getTotalStatistics(List<Long> parklotIds);

	/**
	 * 统计流量
	 *
	 * @param parklotIds
	 * @return
	 */
	TotalStatisticsDTO findParkingTraffic(List<Long> parklotIds);

	/**
	 * 收入统计
	 *
	 * @param parkLotIds
	 * @return
	 */
	FinancialRateStatistics financialRate(List<Long> parkLotIds);

	/**
	 * 车场车位使用统计
	 *
	 * @param parkLotIds
	 * @return
	 */
	List<ParkingOrderRemainPlaceVO> remainPlaceTopFive(List<Long> parkLotIds);

	/**
	 * 获取车场最新的出入场记录
	 *
	 * @param parklotId
	 * @return
	 */
	JHParkingOrderVO getLastParkingOrder(String parklotId);

	/**
	 * 停车支付方式比例
	 *
	 * @param parklotId
	 * @return
	 */
	List<PayTypePercentVO> payTypePercent(String parklotId);

	/**
	 * 根据时间期间获取车辆已使用总时长
	 *
	 * @param cardIds   cardIds
	 * @param parkLotId cardIds
	 * @param startDate startDate
	 * @param endDate   endDate
	 * @return long
	 */
	Long getUsedTimeLength(List<Long> cardIds, Long parkLotId, Date startDate, Date endDate, String plate);

	/**
	 * 总车辆数
	 * @param tenantId
	 * @param parkLotIds
	 * @return
	 */
	public Integer countVehicleNum(String tenantId,  List<Long> parkLotIds);

	/**
	 * 新增车辆数
	 * @param tenantId
	 * @param parkLotIds
	 * @return
	 */
	Integer countAddVehicleNum( String tenantId, List<Long> parkLotIds);


	IPage<ParkingOrderUserVO> countUserParkingOrder(ParkingOrderUserVO parkingOrderUserVO, IPage<ParkingOrderUserVO> page);

	/**
	 * 导出收费员订单
	 * @param response
	 * @param parkingOrderUserVO
	 */
	void exportParkingOrderUsert(HttpServletResponse response, ParkingOrderUserVO parkingOrderUserVO);

	/**
     * 获取停车订单统计信息
     *
     * @param cleanLog 清理日志
     * @return {@link ParkingOrderStatistics}
     */
	ParkingOrderStatistics parkingOrderStatistics(CorpseCleanLogDTO cleanLog);

	/**
	 * 获取停车场当前的停车数量
	 *
	 * @param parklotId 停车场ID
	 * @return 当前停车数量
	 */
	Integer getCurrentAmount(Long parklotId);

	/**
	 * 获取今日进出车辆数量
	 *
	 * @param parklotId 停车场ID
	 * @return 今日进出车辆数量
	 */
	Integer getTodayInAndOutAmount(Long parklotId);

	/**
	 * 累计进出场车辆
	 *
	 * @param parklotId 停车场ID
	 * @return 返回进出场车辆的列表
	 */
	List<TotalInAndOutCarVO> totalInAndOutCar(Long parklotId);

	/**
	 * 入场停车时长分析
	 *
	 * @param parklotId 停车场ID
	 * @return 返回停车时长的统计信息
	 */
	ParkingCarDuringVO parkingCarDuring(Long parklotId);

	/**
	 * 车辆入场时间分布
	 *
	 * @param parklotId 停车场ID
	 * @return 返回车辆入场时间的分布情况
	 */
	List<Map> parkingEnterTimeDistribute(Long parklotId);
}






