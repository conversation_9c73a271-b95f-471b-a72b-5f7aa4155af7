package org.springblade.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@TableName("c_parking_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Parking对象", description = "Parking对象")
public class ParkingOrder extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 停车场ID
	 */
	@ApiModelProperty(value = "停车场ID")
	private Long parklotId;


	/**
	 * 2车辆在场；
	 * 4车辆已出场；
	 * 10 重复进场
	 */
	@ApiModelProperty(value = "2车辆在场； 4车辆已出场； 10重复进场")
	private Integer parkingStatus;

	/**
	 * 目标ID
	 * 重复进场状态时-指向最新进场记录
	 */
	@ApiModelProperty(value = "目标ID 重复进场状态时-指向最新进场记录")
	private Long targetParkingId;
	/**
	 * 车牌号
	 */
	@ApiModelProperty(value = "车牌号")
	private String plate;
	/**
	 * 类型
	 * 0临时车
	 * 1全天
	 * 2分时
	 * 3黑名单
	 * 4白名单
	 */
	@ApiModelProperty(value = "类型 0临时车 1全天 2分时 3黑名单 4白名单")
	private Integer cardType;
	/**
	 * 如果是月卡车，对应卡号
	 */
	@ApiModelProperty(value = "如果是月卡车，对应卡号")
	private Long cardId;
	/**
	 * 是否车位
	 */
	@ApiModelProperty(value = "是否车位")
	private Boolean occupied;
	/**
	 * 停车时长
	 */
	@ApiModelProperty(value = "停车时长")
	private String durationTime;
	/**
	 * 进场通道ID
	 */
	@ApiModelProperty(value = "进场通道ID")
	private Long enterChannelId;
	/**
	 * 进场方式 1摄像头识别 2车主扫码 3手动开闸 4模拟入场
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "进场方式 1摄像头识别 2车主扫码 3手动开闸 4模拟入场")
	private Integer enterWay;
	/**
	 * 进场时间 时间戳
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "进场时间 时间戳")
	private Date enterTime;
	/**
	 * 进场照片
	 */
	@ApiModelProperty(value = "进场照片")
	private String enterImageUrl;
	/**
	 * 入场开杆时间耗时 单位毫秒
	 */
	@ApiModelProperty(value = "入场开杆时间耗时 单位毫秒")
	private Integer enterOpenGateCost;
	/**
	 * 出场通道ID
	 */
	@ApiModelProperty(value = "出场通道ID")
	private Long exitChannelId;

	/**
	 * 出场方式 1摄像头识别 2车主扫码 3手动开闸 4模拟入场
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "出场方式 1摄像头识别 2车主扫码 3手动开闸 4模拟入场")
	private Integer exitWay;

	/**
	 * 出场时间 时间戳
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "出场时间 时间戳")
	private Date exitTime;
	/**
	 * 出场照片
	 */
	@ApiModelProperty(value = "出场照片")
	private String exitImageUrl;
	/**
	 * 出场开杆时间耗时 单位毫秒
	 */
	@ApiModelProperty(value = "出场开杆时间耗时 单位毫秒")
	private Integer exitOpenGateCost;
	/**
	 * 收费规则ID
	 */
	@ApiModelProperty(value = "收费规则ID")
	private Long chargeRuleId;
	/**
	 * 缴费方式，多个id,逗号隔开
	 */
	@ApiModelProperty(value = "缴费方式，多个id,逗号隔开")
	private String payTypes;
	/**
	 * 总金额
	 */
	@ApiModelProperty(value = "总金额")
	private BigDecimal totalAmount;
	/**
	 * 实收金额
	 */
	@ApiModelProperty(value = "实收金额")
	@JsonSerialize(using = ToStringSerializer.class)
	private BigDecimal paidAmount;
	/**
	 * 优惠金额
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "优惠金额")
	private BigDecimal discountAmount;
	/**
	 * 异常金额
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常金额")
	private BigDecimal unusualAmount;

	@ApiModelProperty(value = "应收商户金额")
	private BigDecimal merchantAmount;

	@ApiModelProperty(value = "优惠卷优惠金额")
	private BigDecimal couponDiscountAmount;

	/**
	 * 付款订单，多个id,逗号隔开
	 */
	@ApiModelProperty(value = "付款订单，多个id,逗号隔开")
	private String orderIds;
	/**
	 * 优惠券，多个id,逗号隔开
	 */
	@ApiModelProperty(value = "优惠券，多个id,逗号隔开")
	private String couponIds;
	/**
	 * 优惠券对应的商户，多个id,逗号隔开
	 */
	@ApiModelProperty(value = "优惠券对应的商户，多个id,逗号隔开")
	private String merchantIds;
	/**
	 * 备注，描述
	 */
	@ApiModelProperty(value = "备注，描述")
	private String memo;
	/**
	 * 异常
	 * 0 无异常
	 * 1 重复入场 详情请看targetParkingId
	 */
	@ApiModelProperty(value = "异常 0无异常 1重复入场 详情请看targetParkingId")
	private String reasonIds;
	private Integer version;
	/**
	 * 0 无异常
	 * 1 有异常未处理
	 * 2 有异常已处理
	 */
	@ApiModelProperty(value = "0 无异常 1有异常未处理 2有异常已处理")
	private Integer errorStatus;
	private Long exitOptUserId;
	private Long enterOptUserId;
	/**
	 * 计费开始时间
	 */
	@ApiModelProperty(value = "计费开始时间")
	private Date billingStartTime;
	/**
	 * 进场代办ID
	 */
	@ApiModelProperty(value = "进场代办ID")
	private Long enterTodoId;
	/**
	 * 出场代办ID
	 */
	@ApiModelProperty(value = "出场代办ID")
	private Long exitTodoId;
	/**
	 * 异常金额类型 1多收 2少收
	 */
	@ApiModelProperty(value = "异常金额类型 1多收 2少收")
	private Integer unusualAmountType;
	/**
	 * 临停标识 0-否 1-是
	 */
	@ApiModelProperty(value = "临停标识 0-否 1-是")
	private Boolean tempCar;
	/**
	 * 微信ID
	 */
	@ApiModelProperty(value = "微信ID")
	private String openId;
	/**
	 * 月卡id集合
	 */
	@ApiModelProperty(value = "月卡id集合")
	@TableField("temp_card_Ids")
	private String tempCardIds;

	/**
	 * 预约订单号
	 */
	@ApiModelProperty(value = "预约订单号（仅预约车使用）")
	private String reserveOrderId;

	/**
	 * 其他标识 0:场内停车,1:路边停车
	 */
	@ApiModelProperty(value = "其他标识 0:场内停车,1:路边停车")
	private Integer otherFlag;

	/**
	 * 进场确认;false:未确认,true:已确认
	 */
	@ApiModelProperty(value = "进场确认;false:未确认,true:已确认")
	private boolean enterConfirm;


	@ApiModelProperty(value = "'车位Id'")
	private Long placeId;

	@ApiModelProperty(value = "停车时间节点")
	private String parkingTimeNode;

	@ApiModelProperty(value = "停车时间节点计费详细")
	private String timeNodeDetail;

	@ApiModelProperty(value = "多种方式使用免费权停车业务ID")
	private Long relationId;


	/**
	 * 非空字段初始化
	 *
	 * @return
	 */
	public ParkingOrder() {
		this.plate = "";
		this.totalAmount = BigDecimal.ZERO;
		this.unusualAmount = BigDecimal.ZERO;
		this.paidAmount = BigDecimal.ZERO;
		this.discountAmount = BigDecimal.ZERO;
		this.tempCar = true;
	}
}


