
package com.lecent.payment.utils;



import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Slf4j
public class TimeValidatorUtil {

	private static final long MAX_TIME_DIFFERENCE_MINUTES = 2 * 24 * 60; // 允许的最大时间误差（分钟）
	private static final long MAX_DAYS_BETWEEN_START_AND_END = 30; // 允许的最大进场和出场时间间隔（天）
	private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


	/**
	 * 验证时间是否有效，支持直接传递Date类型的时间
	 *
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 如果时间有效返回true，否则返回false
	 */
	public static boolean isValidTime(Date startTime, Date endTime) {
		// 将Date类型的时间转换为LocalDateTime类型
		LocalDateTime startLocalDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		LocalDateTime endLocalDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		// 调用现有的方法进行验证
		return isValidTime(startLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),endLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
	}
	/**
	 * 验证时间是否有效
	 *
	 * @param strStartTime 开始时间字符串，格式为"yyyy-MM-dd HH:mm:ss"
	 * @param strEndTime 结束时间字符串，格式为"yyyy-MM-dd HH:mm:ss"
	 * @return 如果时间有效返回true，否则返回false
	 */
	public static boolean isValidTime(String strStartTime, String strEndTime) {
		try {
			// 获取当前时间
			LocalDateTime now = LocalDateTime.now();

			// 解析开始时间和结束时间字符串为LocalDateTime对象
			LocalDateTime startTime = LocalDateTime.parse(strStartTime, FORMATTER);
			LocalDateTime endTime = LocalDateTime.parse(strEndTime, FORMATTER);

			// 计算时间差
			long minutesBetweenStartAndEnd = ChronoUnit.MINUTES.between(startTime, endTime);
			long minutesBetweenEndAndNow = ChronoUnit.MINUTES.between(endTime, now);

			// 打印当前时间、开始时间和结束时间
			log.info("当前时间: {}, 进场时间: {}, 出场时间: {}, 进与出时间相差分钟数: {}, 出与当前时间相差分钟数: {}",
				now, startTime, endTime, minutesBetweenStartAndEnd, minutesBetweenEndAndNow);

			String msg = String.format("当前时间: %s, 进场时间: %s, 出场时间: %s", now, startTime, endTime);

			// 检查进场时间和出场时间是否相等或进场时间晚于出场时间
			if (startTime.equals(endTime) || startTime.isAfter(endTime)) {
				log.warn("{} - 进场时间和出场时间不合法", msg);
				return false;
			}

			// 检查出场时间是否早于当前时间且超出5分钟
			if (minutesBetweenEndAndNow < 0 && Math.abs(minutesBetweenEndAndNow) > 5) {
				log.warn("{} - 出场时间早于当前时间", msg);
				return false;
			}

			// 检查出场时间和当前时间是否超过60分钟
			if (minutesBetweenEndAndNow > MAX_TIME_DIFFERENCE_MINUTES) {
				log.warn("{} - 出场时间和当前时间超过{}分钟", msg, MAX_TIME_DIFFERENCE_MINUTES);
				return false;
			}

			// 检查进场时间和出场时间是否超过30天
			if (ChronoUnit.DAYS.between(startTime, endTime) > MAX_DAYS_BETWEEN_START_AND_END) {
				log.warn("{} - 进场时间和出场时间超过{}天", msg,MAX_DAYS_BETWEEN_START_AND_END);
				return false;
			}

			// 如果所有检查都通过，则返回true
			return true;
		} catch (Exception e) {
			log.error("时间格式错误: {}", e.getMessage());
			return false;
		}
	}
}
