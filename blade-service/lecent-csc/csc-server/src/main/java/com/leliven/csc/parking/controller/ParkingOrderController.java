package com.leliven.csc.parking.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.entity.ParkingDetailDTO;
import com.leliven.csc.common.aop.ParkAuth;
import com.leliven.csc.gateway.rpc.parking.dto.ParkingQueryParamDTO;
import com.leliven.csc.gateway.rpc.parking.fegin.IParkingOrderClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023.04.10
 * 停车记录
 */

@RestController
@RequestMapping("/parking/order")
@Api(value = "停车订单", tags = "停车订单")
public class ParkingOrderController {

	@Resource
	private IParkingOrderClient parkingOrderService;

	/**
	 * 临停订单
	 */
	@ParkAuth("parkLotIds")
	@GetMapping("/getPendingOrderList")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "临停订单列表", notes = "")
	public R getPendingOrderList(ParkingQueryParamDTO parkingQueryParamDTO) {
		return parkingOrderService.getPendingOrderList(parkingQueryParamDTO);
	}

	/**
	 * 修改车牌
	 */
	@GetMapping("/putPlate")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "修改车牌", notes = "")
	public R putPlate(ParkingQueryParamDTO parkingQueryParamDTO) {
		return parkingOrderService.putPlate(parkingQueryParamDTO);
	}

	/**
	 * 查询停车记录
	 */
	@PostMapping("/updateParkingOrder")
	@ApiOperation(value = "修改停车信息", notes = "传入parking")
	public R<Boolean> updateParkingOrder(@RequestBody ParkingOrderDTO parking){
		return parkingOrderService.updateParkingOrder(parking);
	}
	/**
	 * 获取修改后的费用
	 */
	@PostMapping("/getUpdateParkingOrderAmount")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "获取修改后的费用（客服系统调用）", notes = "传入parking")
	public R<ParkingDetailDTO> getUpdateParkingOrderAmount(@RequestBody ParkingOrderDTO parking){
		return parkingOrderService.getUpdateParkingOrderAmount(parking);
	}

	/**
	 * 异常订单退款处理
	 */
	@PostMapping("/refundAbnormalOrder")
	@ApiOperation(value = "异常订单退款处理", notes = "传入parkingOrderId")
	public R<Boolean> refundAbnormalOrder(@RequestParam("parkingOrderId") Long parkingOrderId) {
		return parkingOrderService.refundAbnormalOrder(parkingOrderId);
	}

	/**
	 * 异常订单无需处理
	 */
	@PostMapping("/ignoreAbnormalOrder")
	@ApiOperation(value = "异常订单无需处理", notes = "传入parkingOrderId")
	public R<Boolean> ignoreAbnormalOrder(@RequestParam("parkingOrderId") Long parkingOrderId) {
		return parkingOrderService.ignoreAbnormalOrder(parkingOrderId);
	}
}