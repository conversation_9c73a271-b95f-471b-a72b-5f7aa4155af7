package com.leliven.csc.parking.fegin;

import com.lecent.park.vo.ParkingOrderVO;
import com.leliven.csc.alarm.dto.ChannelTodoParamDTO;
import com.leliven.csc.gateway.rpc.parking.dto.ParkingQueryParamDTO;
import com.leliven.csc.parking.fegin.fallback.ParkingRecordClientFallback;
import com.lecent.park.dto.space.ParkingPlaceDTO;
import com.lecent.park.entity.ParklotDeviceRet;
import org.springblade.common.constant.LecentAppConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 停车记录
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
@Component
@FeignClient(name = LecentAppConstant.APPLICATION_PARK_NAME, fallbackFactory = ParkingRecordClientFallback.class)
public interface IParkingRecordClient {

	/**
	 * 获取停车记录列表
	 *
	 * @param parkingQueryParamDTO 查询参数
	 * @return 停车记录列表
	 */
	@GetMapping("/parkingOrder/list")
	R getParkingRecordList(@SpringQueryMap ParkingQueryParamDTO parkingQueryParamDTO);

	/**
	 * 获取车位详情
	 *
	 * @param parkingPlace 车位信息
	 * @return 车位详情
	 */
	@GetMapping("/parkingplace/detail")
	R getSpaceDetail(ParkingPlaceDTO parkingPlace);

	/**
	 * 获取特殊放行记录列表
	 *
	 * @param channelTodo 查询参数
	 * @return 特殊放行记录列表
	 */
	@GetMapping("/channelTodo/list")
	R getSpecialPassList(@SpringQueryMap ChannelTodoParamDTO channelTodo);

	/**
	 * 获取路边停车记录列表
	 *
	 * @param parkingQueryParamDTO 查询参数
	 * @return 路边停车记录列表
	 */
	@GetMapping("/roadsideClient/getRoadsideParkingList")
	R getRoadsideParkingList(@SpringQueryMap ParkingQueryParamDTO parkingQueryParamDTO);


	/**
	 * 获取停车记录详情
	 *
	 * @param parkingQueryParamDTO 查询参数
	 * @return 停车记录详情
	 */
	@GetMapping("/parkingOrder/detail")
	R<ParkingOrderVO> getParkingOrderDetail(@SpringQueryMap ParkingQueryParamDTO parkingQueryParamDTO);

	/**
	 * 根据车牌获取欠费记录
	 *
	 * @param plate 车牌号
	 * @return 欠费记录列表
	 */
	@GetMapping("/parkingunpaidorder/getArrearsRecordByPlate")
	List<Map<String, Object>> getArrearsRecordByPlate(@RequestParam("plate") String plate);

	/**
	 * 获取车位绑定设备列表
	 *
	 * @param placeId 车位ID
	 * @return 车位绑定设备列表
	 */
	@GetMapping("/parkingplace/getPlaceBindDevice/{id}")
	List<ParklotDeviceRet> getPlaceBindDevice(@PathVariable("id") Long placeId);
}
