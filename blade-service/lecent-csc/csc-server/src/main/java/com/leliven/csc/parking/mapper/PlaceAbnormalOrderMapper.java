package com.leliven.csc.parking.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.csc.parking.dto.PlaceAbnormalOrderDTO;
import com.leliven.csc.parking.vo.PlaceAbnormalOrderVO;


@Mapper
public interface PlaceAbnormalOrderMapper {

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param dto  查询条件
     * @return 分页对象
     */
    List<PlaceAbnormalOrderVO> selectPage(IPage<PlaceAbnormalOrderVO> page, @Param("dto") PlaceAbnormalOrderDTO dto);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 详情
     */
    PlaceAbnormalOrderVO selectById(@Param("id") Long id);

    /**
     * 获取冲突的停车订单
     *
     * @param dto 查询条件
     * @return 冲突的停车订单
     */
    List<PlaceAbnormalOrderVO> selectParkingOrders(@Param("dto") PlaceAbnormalOrderDTO dto);

    /**
     * 抓拍记录
     *
     * @param dto 查询条件
     * @return 抓拍记录
     */
    List<PlaceAbnormalOrderVO> selectCaptureRecord(@Param("dto") PlaceAbnormalOrderDTO dto);

    /**
     * 时间轴
     *
     * @param placeId 车位id
     * @return 时间轴
     */
    List<PlaceAbnormalOrderVO> selectTimeAxis(@Param("placeId") Long placeId);

    /**
     * 获取在场停车记录
     *
     * @param placeId 车位id
     * @return 在场停车记录
     */
    PlaceAbnormalOrderVO.PresenceParkingOrder selectPresenceParkingOrder(@Param("placeId") Long placeId);
}
