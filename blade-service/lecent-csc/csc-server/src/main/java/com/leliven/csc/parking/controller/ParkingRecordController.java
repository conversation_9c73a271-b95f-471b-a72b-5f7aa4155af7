package com.leliven.csc.parking.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.dto.TempParkingOrderDTO;
import com.lecent.park.dto.space.ParkingPlaceDTO;
import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.entity.ParklotDeviceRet;
import com.lecent.park.feign.IParklotDeviceRetClient;
import com.lecent.park.vo.CscCommonQueryParam;
import com.lecent.park.vo.ParkingOrderVO;
import com.leliven.csc.alarm.dto.ChannelTodoParamDTO;
import com.leliven.csc.common.aop.ParkAuth;
import com.leliven.csc.common.utils.ParkAuthUtil;
import com.leliven.csc.gateway.rpc.parking.dto.ParkingQueryParamDTO;
import com.leliven.csc.parking.fegin.IParkingRecordClient;
import com.leliven.csc.parking.service.IParkingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.common.entity.KeyValueVo;
import org.springblade.common.utils.StrUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023.04.10
 * 停车记录
 */
@RestController
@RequestMapping("/parking/record")
@Api(value = "停车记录", tags = "停车记录")
public class ParkingRecordController {

    @Resource
    private IParkingRecordService parkingRecordService;

    @Resource
    private IParkingRecordClient parkingRecordClient;

    @Resource
    private IParklotDeviceRetClient parklotDeviceRetClient;

    /**
     * 欠费明细记录
     */
    @ParkAuth("parklotIds")
    @GetMapping("/getArrearsDetailList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "欠费记录", notes = "")
    public R getArrearsDetailList(CscCommonQueryParam queryParam) {
        if (Func.isBlank(queryParam.getAscs()) && Func.isBlank(queryParam.getDescs())) {
            //默认欠费金额
            queryParam.setDescs("totalArrearsMoney");
        } else {
            queryParam.setAscs(queryParam.getAscs());
            queryParam.setDescs(queryParam.getDescs());
        }
        Condition.getPage(queryParam);
        queryParam.setTenantId(AuthUtil.getTenantId());
        return R.data(parkingRecordService.countArrearsStatistics(Condition.getPage(queryParam), queryParam));
    }

    /**
     * 欠费记录
     */
    @ParkAuth("parklotIds")
    @GetMapping("/getArrearsStatisticsList")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "欠费明细", notes = "")
    public R getArrearsStatisticsList(CscCommonQueryParam commonQueryParam) {
        return R.data(parkingRecordService.getArrearsDetailList(Condition.getPage(commonQueryParam), commonQueryParam));
    }

    /**
     * 导出欠费订单
     */
    @ParkAuth("parklotIds")
    @PostMapping("/arrears-order/export")
    @ApiOperation(value = "导出欠费订单", notes = "传入recoveredOrder")
    public void exportArrearsDetailOrder(HttpServletResponse response, CscCommonQueryParam commonQueryParam) {
        parkingRecordService.exportArrearsDetailList(response, commonQueryParam);
    }

    /**
     * 导出欠费详情
     */
    @ParkAuth("parklotIds")
    @PostMapping("/arrears-order-detail/export")
    @ApiOperation(value = "导出欠费详情", notes = "传入recoveredOrder")
    public void exportArrearsStatisticsList(HttpServletResponse response, CscCommonQueryParam commonQueryParam) {
        parkingRecordService.exportArrearsStatisticsList(response, commonQueryParam);
    }

    /**
     * 路边停车
     */
    @ParkAuth("parkLotIds")
    @GetMapping("/getRoadsideParkingList")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "路边停车", notes = "")
    public R getRoadsideParkingList(ParkingQueryParamDTO parkingQueryParamDTO) {
        if (Func.isNotEmpty(parkingQueryParamDTO.getParklotIdStr())) {
            parkingQueryParamDTO.setParkLotIds(Func.toLongList(parkingQueryParamDTO.getParklotIdStr()));
        }
        return parkingRecordService.getParkingRecordList(parkingQueryParamDTO);
    }

    /**
     * 路边停车
     */
    @GetMapping("/getAbnormalParkingOrderList")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "异常订单", notes = "")
    public R<IPage<ParkingOrderVO>> getAbnormalParkingOrderList(ParkingOrderDTO parkingOrderDTO, Query query) {
        parkingOrderDTO.setParkLotIds(parkingOrderDTO.mergeParkLotId());
        parkingOrderDTO.setParkLotIds(ParkAuthUtil.parkAuth(parkingOrderDTO.getParkLotIds()));
        parkingOrderDTO.setParklotType(ParkLotType.ROAD_IN.getValue());
        return R.data(parkingRecordService.getAbnormalParkingOrderList(Condition.getPage(query), parkingOrderDTO));
    }

    /**
     * 获取停车记录详情
     */
    @ParkAuth("parkLotIds")
    @GetMapping("/getParkingOrderDetail")
    @ApiOperation(value = "获取停车记录详情")
    public R<ParkingOrderVO> getParkingOrderDetail(ParkingQueryParamDTO parkingQueryParamDTO) {
        R<ParkingOrderVO> parkingOrderVOR = parkingRecordClient.getParkingOrderDetail(parkingQueryParamDTO);
        if (parkingOrderVOR != null && parkingOrderVOR.isSuccess() && parkingOrderVOR.getData() != null && parkingOrderVOR.getData().getPlaceId() != null) {
            R<List<ParklotDeviceRet>> retR = parklotDeviceRetClient.listByParkPlaceId(parkingOrderVOR.getData().getPlaceId());
            if (retR != null && Func.isNotEmpty(retR.getData())) {
                List<ParklotDeviceRet> deviceRets = retR.getData();
                Map<Integer, ParklotDeviceRet> deviceMap = new HashMap<>(deviceRets.size());
                deviceRets.forEach(device -> {
                    deviceMap.put(device.getDeviceType(), device);
                });
                parkingOrderVOR.getData().setDeviceMap(deviceMap);
            }
        }
        return parkingOrderVOR;
    }

    /**
     * 车位详情
     */
    @GetMapping("/getSpaceDetail")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "车位详情", notes = "传入parkingPlace")
    public R getDetail(ParkingPlaceDTO parkingPlace) {
        return parkingRecordClient.getSpaceDetail(parkingPlace);
    }

    /**
     * 特殊放行记录
     */
    @GetMapping("/getSpecialPassList")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "特殊放行记录", notes = "传入todo")
    public R getSpecialPassList(ChannelTodoParamDTO channelTodo) {
        return parkingRecordClient.getSpecialPassList(channelTodo);
    }

    /**
     * 查询已缴未交款订单
     */
    @GetMapping("/findTempOrderPage")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "查询已缴未交款订单", notes = "传入parkingOrder")
    public R findTempOrderPage(TempParkingOrderDTO parkingOrder, Query query) {
        if (Func.isBlank(query.getAscs()) && Func.isBlank(query.getDescs())) {
            //默认按照时间排序
            query.setDescs("enter_time");
        } else {
            query.setAscs(StrUtil.camelToUnderlines(query.getAscs()));
            query.setDescs(StrUtil.camelToUnderlines(query.getDescs()));
        }

        parkingOrder.setParkLotIds(parkingOrder.mergeParkLotId());
        parkingOrder.setParkLotIds(ParkAuthUtil.parkAuth(parkingOrder.getParkLotIds()));
        return R.data(parkingRecordService.findTempOrderPage(Condition.getPage(query), parkingOrder));
    }

    @GetMapping("/findAbnormalHandlerPersonList")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "获取异常处理人列表", notes = "")
    public R<List<KeyValueVo>> findAbnormalHandlerPersonList() {
        return R.data(parkingRecordService.findAbnormalHandlerPersonList());
    }

    @GetMapping("/countAbnormalOrder")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "异常订单总数", notes = "")
    public R<Map<String, Map<String, Object>>> countAbnormalOrder(ParkingOrderAbnormalDTO abnormalDTO) {
        abnormalDTO.setParkLotIds(abnormalDTO.mergeParkLotId());
        abnormalDTO.setParkLotIds(ParkAuthUtil.parkAuth(abnormalDTO.getParkLotIds()));
        abnormalDTO.setParklotType(ParkLotType.ROAD_IN.getValue());
        return R.data(parkingRecordService.countAbnormalOrder(abnormalDTO));
    }

    @GetMapping("/countAbnormalHandlerOrder")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "统计已处理的订单", notes = "")
    public R<Map<String, Map<String, Object>>> countAbnormalHandlerOrder(ParkingOrderAbnormalDTO abnormalDTO) {
        abnormalDTO.setParkLotIds(abnormalDTO.mergeParkLotId());
        abnormalDTO.setParkLotIds(ParkAuthUtil.parkAuth(abnormalDTO.getParkLotIds()));
        abnormalDTO.setParklotType(ParkLotType.ROAD_IN.getValue());
        return R.data(parkingRecordService.countAbnormalHandlerOrder(abnormalDTO));
    }

    @GetMapping("/countAbnormalHandlerOrderByChart")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "图表", notes = "")
    public R<List<Map<String, Object>>> countAbnormalHandlerOrderByChart(ParkingOrderAbnormalDTO abnormalDTO) {
        abnormalDTO.setParkLotIds(abnormalDTO.mergeParkLotId());
        abnormalDTO.setParkLotIds(ParkAuthUtil.parkAuth(abnormalDTO.getParkLotIds()));
        abnormalDTO.setParklotType(ParkLotType.ROAD_IN.getValue());
        return R.data(parkingRecordService.countAbnormalHandlerOrderByChart(abnormalDTO));
    }

}
