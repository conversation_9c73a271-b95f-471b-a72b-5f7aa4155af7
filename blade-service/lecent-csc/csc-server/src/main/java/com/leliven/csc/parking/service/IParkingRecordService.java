package com.leliven.csc.parking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.dto.TempParkingOrderDTO;
import com.lecent.park.vo.ArrearsOrderVO;
import com.lecent.park.vo.CscCommonQueryParam;
import com.lecent.park.vo.OrderPageStatisticVO;
import com.lecent.park.vo.ParkingOrderVO;
import com.leliven.csc.gateway.rpc.parking.dto.ParkingQueryParamDTO;
import org.springblade.common.entity.KeyValueVo;
import org.springblade.core.tool.api.R;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface IParkingRecordService {

    /**
     * 获取停车记录列表
     *
     * @param parkingQueryParamDTO 查询参数
     * @return 停车记录列表
     */
    R<IPage<Map<String, Object>>> getParkingRecordList(ParkingQueryParamDTO parkingQueryParamDTO);

    /**
     * 查询异常订单
     *
     * @param parkingOrderDTO
     * @return
     */
    IPage<ParkingOrderVO> getAbnormalParkingOrderList(IPage<ParkingOrderVO> page, ParkingOrderDTO parkingOrderDTO);

    IPage<OrderPageStatisticVO> findTempOrderPage(IPage<OrderPageStatisticVO> page, TempParkingOrderDTO parkingOrder);

    IPage<ArrearsOrderVO> getArrearsStatisticsList(IPage<ArrearsOrderVO> page, CscCommonQueryParam queryParam);

    /**
     * 统计欠费记录
     *
     * @param page       页
     * @param queryParam 查询参数
     * @return {@link IPage }<{@link Map }<{@link String }, {@link Object }>>
     */
    IPage<ArrearsOrderVO> countArrearsStatistics(IPage<ArrearsOrderVO> page, CscCommonQueryParam queryParam);

    List<Map<String, Object>> getArrearsMerchantGroup(CscCommonQueryParam queryParam);

    IPage<Map<String, Object>> getArrearsDetailList(IPage<Map<String, Object>> page, CscCommonQueryParam commonQueryParam);

    List<KeyValueVo> findAbnormalHandlerPersonList();

    /**
     * 统计未处理的差异订单
     *
     * @param abnormalDTO
     * @return
     */
    Map<String, Map<String, Object>> countAbnormalOrder(ParkingOrderAbnormalDTO abnormalDTO);

    /**
     * 统计已处理的订单
     *
     * @param abnormalDTO
     * @return
     */
    Map<String, Map<String, Object>> countAbnormalHandlerOrder(ParkingOrderAbnormalDTO abnormalDTO);

    /**
     * 图表
     *
     * @param abnormalDTO
     * @return
     */
    List<Map<String, Object>> countAbnormalHandlerOrderByChart(ParkingOrderAbnormalDTO abnormalDTO);

    /**
     * 导出欠费订单
     *
     * @param response
     * @param commonQueryParam
     */
    void exportArrearsDetailList(HttpServletResponse response, CscCommonQueryParam commonQueryParam);

    /**
     * 导出欠费明细
     *
     * @param response
     * @param commonQueryParam
     */
    void exportArrearsStatisticsList(HttpServletResponse response, CscCommonQueryParam commonQueryParam);
}
