package com.leliven.csc.parking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.vo.ParkingOrderVO;

import java.util.List;
import java.util.Map;

public interface IParkingOrderAbnormalService {
    /**
     * 欠缴
     *
     * @param page
     * @param parkingOrderDTO
     * @return
     */
    IPage<ParkingOrderVO> findArrearsDetailList(IPage<ParkingOrderVO> page, ParkingOrderDTO parkingOrderDTO);

    /**
     * 总条数
     *
     * @param parkingOrderDTO
     * @return
     */
    Long countArrearsNum(ParkingOrderDTO parkingOrderDTO);

    ParkingOrderVO findArrearsDetail(ParkingOrderDTO parkingOrderDTO);

    /**
     * 锁异常处理
     *
     * @param orderAbnormalDTO
     * @return
     */
    Boolean lockOrderErrorHandle(ParkingOrderAbnormalDTO orderAbnormalDTO);

    /**
     * 图表
     *
     * @param abnormalDTO
     * @return
     */
    List<Map<String, Object>> countArrears(ParkingOrderAbnormalDTO abnormalDTO);
}
