package com.leliven.csc.parking.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.csc.parking.dto.PlaceAbnormalOrderDTO;
import com.leliven.csc.parking.vo.PlaceAbnormalOrderVO;

public interface IPlaceAbnormalOrderService {

    /**
     * 树形结构
     *
     * @param dto 查询条件
     * @return 树形结构
     */
    List<PlaceAbnormalOrderVO> tree(PlaceAbnormalOrderDTO dto);

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @param dto  查询条件
     * @return 分页对象
     */
    IPage<PlaceAbnormalOrderVO> pagePlace(IPage<PlaceAbnormalOrderVO> page, PlaceAbnormalOrderDTO dto);

    /**
     * 详情
     *
     * @param id 主键
     * @return 详情
     */
    PlaceAbnormalOrderVO detail(Long id);

    /**
     * 获取冲突的停车订单
     *
     * @param dto 查询条件
     * @return 冲突的停车订单
     */
    List<PlaceAbnormalOrderVO> parkingOrders(PlaceAbnormalOrderDTO dto);

    /**
     * 抓拍记录
     *
     * @param dto 查询条件
     * @return 抓拍记录
     */
    List<PlaceAbnormalOrderVO> captureRecord(PlaceAbnormalOrderDTO dto);

    /**
     * 时间轴
     *
     * @param placeId 车位id
     * @return 时间轴
     */
    List<PlaceAbnormalOrderVO> timeAxis(Long placeId);

    /**
     * 入场
     *
     * @param dto 查询条件
     * @return 入场
     */
    Boolean enter(PlaceAbnormalOrderDTO dto);

    /**
     * 出场
     *
     * @param dto 查询条件
     * @return 出场
     */
    Boolean exit(PlaceAbnormalOrderDTO dto);

    /**
     * 退款
     *
     * @param dto 查询条件
     * @return 退款
     */
    Boolean refund(PlaceAbnormalOrderDTO dto);

    /**
     * 删除
     *
     * @param dto 查询条件
     * @return 删除
     */
    Boolean remove(PlaceAbnormalOrderDTO dto);

    /**
     * 完成
     *
     * @param dto 查询条件
     * @return 完成
     */
    Boolean finish(PlaceAbnormalOrderDTO dto);
}
