package com.leliven.csc.parking.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lecent.park.vo.CscCommonQueryParam;
import com.leliven.csc.parking.service.IParkingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 临停未缴订单
 *
 * <AUTHOR>
 * @date 2023/8/16
 */
@RestController
@RequestMapping("/parking/unpaidOrder")
@Api(value = "临停未缴订单", tags = "临停未缴订单")
public class TempParkingUnpaidOrderController {

	@Resource
	private IParkingRecordService parkingRecordService;

	/**
	 * 临停未缴根据支付商户汇总
	 */
	@GetMapping("/getArrearsMerchantGroup")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入queryParam")
	public R<List<Map<String, Object>>> getArrearsMerchantGroup(CscCommonQueryParam queryParam) {
		return R.data(parkingRecordService.getArrearsMerchantGroup(queryParam));
	}
}
