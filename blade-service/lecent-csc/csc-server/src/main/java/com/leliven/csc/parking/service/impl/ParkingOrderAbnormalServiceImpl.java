package com.leliven.csc.parking.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.en.OrderAbnormalTypeEnum;
import com.lecent.park.entity.ParkingOrderAbnormal;
import com.lecent.park.entity.ParkingPlace;
import com.lecent.park.entity.Parklot;
import com.lecent.park.entity.ParklotDeviceRet;
import com.lecent.park.feign.IParkingOrderAbnormalClient;
import com.lecent.park.feign.IParkingPlaceClient;
import com.lecent.park.feign.IParklotDeviceRetClient;
import com.lecent.park.vo.ParkingOrderVO;
import com.leliven.csc.alarm.enums.StatementCycleTypeEnum;
import com.leliven.csc.application.work.service.WorkOrderAppServiceI;
import com.leliven.csc.application.work.service.WorkOrderConfigAppServiceI;
import com.leliven.csc.client.work.dto.ProcessorDTO;
import com.leliven.csc.client.work.dto.SponsorDTO;
import com.leliven.csc.client.work.dto.WorkOrderSourceDTO;
import com.leliven.csc.client.work.dto.cmd.WorkOrderAddCmd;
import com.leliven.csc.gateway.persistence.work.mysql.dataobject.WorkOrderConfigDO;
import com.leliven.csc.gateway.rpc.parking.fegin.IParkingLotClient;
import com.leliven.csc.parking.service.IParkingOrderAbnormalService;
import com.leliven.csc.work.dto.WorkOrderAttachmentDTO;
import com.leliven.csc.work.dto.WorkOrderAttachmentsDTO;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderAbnormalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.TenantConstant;
import org.springblade.common.utils.ActiveProfilesUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/8/1 9:55
 */
@Slf4j
@Service
@TenantIgnore(tenants = TenantConstant.ADMIN_CODE)
public class ParkingOrderAbnormalServiceImpl extends BaseServiceImpl<ParkingOrderAbnormalMapper, ParkingOrderAbnormal> implements IParkingOrderAbnormalService {

    @Resource
    private IParkingOrderAbnormalClient parkingOrderAbnormalClient;


    @Resource
    private WorkOrderAppServiceI workOrderAppService;
    @Resource
    private IParkingLotClient parkingLotClient;
    @Resource
    private IParkingPlaceClient parkingPlaceClient;
    @Resource
    private IParklotDeviceRetClient parklotDeviceRetClient;

    @Resource
    private IUserClient userClient;
    @Resource
    private WorkOrderConfigAppServiceI workOrderConfigService;

    /**
     * 欠缴
     *
     * @param page
     * @param parkingOrderDTO
     * @return
     */
    @Override
    public IPage<ParkingOrderVO> findArrearsDetailList(IPage<ParkingOrderVO> page, ParkingOrderDTO parkingOrderDTO) {
        List<ParkingOrderVO> recordList = null;
        if (Func.isNotBlank(parkingOrderDTO.getReason())) {
            parkingOrderDTO.setHandlerStatus(1);
        }
        if (parkingOrderDTO.getHandlerStatus() == null || parkingOrderDTO.getHandlerStatus() == 0) {
            page.orders().add(new OrderItem("exit_time", false));
        }
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.baseMapper.getArrearsDetailList(page, parkingOrderDTO);
        } else {
            recordList = this.baseMapper.getArrearsDetailListByDoris(page, parkingOrderDTO);
        }
        page.setRecords(recordList);
        return page;
    }

    /**
     * 总条数
     *
     * @param parkingOrderDTO
     * @return
     */
    @Override
    public Long countArrearsNum(ParkingOrderDTO parkingOrderDTO) {
        parkingOrderDTO.setHandlerStatus(0);
        Query query = new Query();
        query.setSize(1);
        query.setCurrent(1);
        IPage<ParkingOrderVO> page = Condition.getPage(query);
        findArrearsDetailList(page, parkingOrderDTO);
        return page.getPages();
    }


    public List<ParkingOrderVO> findArrearsDetailList(ParkingOrderDTO parkingOrderDTO) {
        List<ParkingOrderVO> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.baseMapper.getArrearsDetailList(null, parkingOrderDTO);
        } else {
            recordList = this.baseMapper.getArrearsDetailListByDoris(null, parkingOrderDTO);
        }
        return recordList;
    }


    @Override
    public ParkingOrderVO findArrearsDetail(ParkingOrderDTO parkingOrderDTO) {
        ParkingOrderDTO param = new ParkingOrderDTO();
        param.setId(parkingOrderDTO.getId());
        List<ParkingOrderVO> parkingOrderVOList = findArrearsDetailList(param);
        if (Func.isEmpty(parkingOrderVOList)) {
            throw new ServiceException("未查询到当前停车数据");
        }
        ParkingOrderVO parkingOrder = parkingOrderVOList.get(0);
        IPage<ParkingOrderVO> page = new Page<>();
        page.setSize(2);
        page.setCurrent(1);
        if (parkingOrderDTO.getNextType() == 1) {
            //上一条
            page.orders().add(new OrderItem("exit_time", true));
            parkingOrderDTO.setExitStartTime(parkingOrder.getExitTime());
        } else {
            //下一条
            page.orders().add(new OrderItem("exit_time", false));
            parkingOrderDTO.setExitEndTime(parkingOrder.getExitTime());
        }
        parkingOrderDTO.setId(null);
        parkingOrderDTO.setHandlerStatus(0);
        List<ParkingOrderVO> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.baseMapper.getArrearsDetailList(page, parkingOrderDTO);
        } else {
            recordList = this.baseMapper.getArrearsDetailListByDoris(page, parkingOrderDTO);
        }
        if (Func.isNotEmpty(recordList)) {
            for (ParkingOrderVO parkingOrderVO : recordList) {
                if (!parkingOrderVO.getId().equals(parkingOrder.getId())) {
                    return parkingOrderVO;
                }
            }
        }
        return null;
    }


    /**
     * 锁异常处理
     *
     * @param orderAbnormalDTO
     * @return
     */
    @Override
    public Boolean lockOrderErrorHandle(ParkingOrderAbnormalDTO orderAbnormalDTO) {
        if (orderAbnormalDTO.getParkingId() == null) {
            throw new ServiceException("停车记录id不能为空");
        }
        if (Func.isBlank(orderAbnormalDTO.getReason())) {
            throw new ServiceException("未缴原因不能为空");
        }

        Long abnormalId = null;

        ParkingOrderAbnormal param = new ParkingOrderAbnormal();
        param.setParkingId(orderAbnormalDTO.getParkingId());
        param.setStatus(1);
        param.setAbnormalType(OrderAbnormalTypeEnum.ABNORMAL_TYPE_5.getValue());
        List<ParkingOrderAbnormal> parkingOrderAbnormals = this.baseMapper.findParkingOrderAbnormalList(param);
        ParkingOrderAbnormal parkingOrderAbnormal = null;
        if (Func.isNotEmpty(parkingOrderAbnormals)) {
            parkingOrderAbnormal = parkingOrderAbnormals.get(0);
            abnormalId = parkingOrderAbnormal.getId();
        }
        ParkingOrderDTO parkingOrderDTO = new ParkingOrderDTO();
        parkingOrderDTO.setId(orderAbnormalDTO.getParkingId());
        parkingOrderDTO.setHandlerStatus(0);
        if (abnormalId != null) {
            parkingOrderDTO.setHandlerStatus(1);
        }
        List<ParkingOrderVO> parkingOrderVOList = findArrearsDetailList(parkingOrderDTO);
        if (Func.isEmpty(parkingOrderVOList)) {
            throw new ServiceException("未查询到停车记录");
        }
        ParkingOrderVO parkingOrderVO = parkingOrderVOList.get(0);

        BladeUser user = AuthUtil.getUser();
        if (parkingOrderAbnormal != null) {
            long time = new Date().getTime() - parkingOrderAbnormal.getCreateTime().getTime();
            if (time < (60 * 1000)) {
                throw new ServiceException("该记录已处理请刷新页面！");
            }
            if (!user.getUserId().equals(parkingOrderAbnormal.getHandlerPersonId())) {
                throw new ServiceException("该订单已被" + parkingOrderAbnormal.getHandlerPersonName() + "处理，只能" + parkingOrderAbnormal.getHandlerPersonName() + "本人可以处理！");
            }
        }
        orderAbnormalDTO.setId(abnormalId);
        orderAbnormalDTO.setHandlerDate(new Date());
        orderAbnormalDTO.setHandlerPersonId(user.getUserId());
        orderAbnormalDTO.setHandlerPersonName(user.getUserName());
        orderAbnormalDTO.setStatus(1);
        orderAbnormalDTO.setAbnormalType(OrderAbnormalTypeEnum.ABNORMAL_TYPE_5.getValue());
        orderAbnormalDTO.setParklotId(parkingOrderVO.getParklotId());
        orderAbnormalDTO.setTenantId(parkingOrderVO.getTenantId());
        orderAbnormalDTO.setTotalAmount(parkingOrderVO.getTotalAmount());
        orderAbnormalDTO.setUnPaidAmount(parkingOrderVO.getUnPaidAmount());
        orderAbnormalDTO.setPaidAmount(parkingOrderVO.getPaidAmount());
        //转工单
        if (orderAbnormalDTO.getIsWorkOrder() != null && orderAbnormalDTO.getIsWorkOrder() == 1) {
            String workOrderCode = createWorkOrderCode(orderAbnormalDTO, parkingOrderVO, user);
            orderAbnormalDTO.setWorkOrderCode(workOrderCode);
        }
        R<Long> longR = parkingOrderAbnormalClient.saveParkingOrderAbnormal(orderAbnormalDTO);
        if (longR != null && longR.getData() != null) {
            return true;
        }
        return false;
    }


    /**
     * 创建工单
     *
     * @param orderAbnormalDTO
     * @return
     */
    private String createWorkOrderCode(ParkingOrderAbnormalDTO orderAbnormalDTO, ParkingOrderVO parkingOrderVO, BladeUser user) {
        if (orderAbnormalDTO.getWorkOrderConfigId() == null) {
            throw new ServiceException("故障现象不能为空");
        }
        WorkOrderConfigDO configDO = workOrderConfigService.getById(orderAbnormalDTO.getWorkOrderConfigId());
        if (configDO == null) {
            throw new ServiceException("故障现象不能为空");
        }
        orderAbnormalDTO.setWorkOrderTitle(configDO.getDisplay());
        R<Parklot> parklotR = parkingLotClient.getByIdExcludeTenantId(parkingOrderVO.getParklotId());
        R<ParkingPlace> parkingPlaceR = parkingPlaceClient.getByIdExcludeTenantId(parkingOrderVO.getPlaceId());
        R<List<ParklotDeviceRet>> retR = parklotDeviceRetClient.listByParkPlaceId(parkingOrderVO.getPlaceId());
        Long deviceId = null;
        if (retR != null && Func.isNotEmpty(retR.getData())) {
            for (ParklotDeviceRet deviceRet : retR.getData()) {
                if (DeviceType.PARKING_LOCK.getValue().equals(deviceRet.getDeviceType())) {
                    deviceId = deviceRet.getDeviceId();
                    break;
                }
            }
        }
        String description = null;
        if (Func.isNotBlank(configDO.getContentTemplate())) {
            description = formatMessage(configDO.getContentTemplate(), parklotR.getData().getName(), parkingPlaceR.getData().getPlaceCode(), configDO.getDisplay());
        }
        WorkOrderAddCmd cmd = new WorkOrderAddCmd();
        cmd.setParkLotId(parkingOrderVO.getParklotId());
        cmd.setParkLotName(parklotR.getData().getName());
        cmd.setPlaceId(parkingOrderVO.getPlaceId());
        cmd.setPlaceCode(parkingPlaceR.getData().getPlaceCode());
        cmd.setDeviceId(deviceId);
        cmd.setDeviceType(DeviceType.PARKING_LOCK.getValue());
        cmd.setTitle(orderAbnormalDTO.getWorkOrderTitle());
        cmd.setConfigId(orderAbnormalDTO.getWorkOrderConfigId());
        cmd.setDescription(description);
        cmd.setRemarks(orderAbnormalDTO.getRemark());
        //发起人
        cmd.setSponsor(new SponsorDTO(user.getUserId(), user.getUserName()));
        cmd.setSource(new WorkOrderSourceDTO(3, 2));
        cmd.setTenantId(parkingOrderVO.getTenantId());
        Map<String, String> extendParams = new HashMap<>();
        extendParams.put("parkingOrderId", parkingOrderVO.getId().toString());
        extendParams.put("plate", parkingOrderVO.getPlate());
        extendParams.put("enterTime", DateUtil.format(parkingOrderVO.getEnterTime(), DateUtil.PATTERN_DATETIME));
        cmd.setExtentParams(extendParams);
        //处理人
        R<List<User>> userR = userClient.listByRoles("ops", null);
        if (userR != null && userR.getData() != null) {
            List<ProcessorDTO> processorDTOS = new ArrayList<>();
            for (User item : userR.getData()) {
                processorDTOS.add(new ProcessorDTO(item.getId(), item.getRealName()));
            }
            cmd.setProcessors(processorDTOS);
        }
        //附件
        if (Func.isNotBlank(orderAbnormalDTO.getWorkOrderAnnex())) {
            WorkOrderAttachmentsDTO attachmentsDTO = new WorkOrderAttachmentsDTO();
            List<WorkOrderAttachmentDTO> attachments = new ArrayList<>();
            WorkOrderAttachmentDTO workOrderAttachmentDTO = new WorkOrderAttachmentDTO();
            workOrderAttachmentDTO.setName(orderAbnormalDTO.getWorkOrderAnnex());
            workOrderAttachmentDTO.setUrl(orderAbnormalDTO.getWorkOrderAnnex());
            workOrderAttachmentDTO.setType("standard");
            attachments.add(workOrderAttachmentDTO);
            attachmentsDTO.addAttachments("standard", attachments);
            cmd.setAttachments(attachmentsDTO);
        }
        log.info("createWorkOrderCode cmd:{}", Func.toJson(cmd));
        boolean b = workOrderAppService.add(cmd);
        if (b) {
            return cmd.getCode();
        }
        throw new ServiceException("创建工单失败");
    }

    public String formatMessage(String template, String parklotName, String placeName, String display) {
        Map<String, String> replacements = new HashMap<>();
        replacements.put("parklotName", parklotName);
        replacements.put("placeName", placeName);
        replacements.put("display", display);
        return replacePlaceholders(template, replacements);
    }

    /**
     * 根据模板参数替换模板中的占位符
     *
     * @param template     模板字符串
     * @param replacements 一个包含占位符名称和对应值的Map
     * @return 替换后的字符串
     */
    public String replacePlaceholders(String template, Map<String, String> replacements) {
        Pattern PLACEHOLDER_PATTERN = Pattern.compile("#\\{\\[([^\\]]+)\\]\\}");
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String placeholderName = matcher.group(1);
            String replacement = replacements.getOrDefault(placeholderName, ""); // 如果找不到占位符对应的值，则使用空字符串替换
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 图表
     *
     * @param abnormalDTO
     * @return
     */
    @Override
    public List<Map<String, Object>> countArrears(ParkingOrderAbnormalDTO abnormalDTO) {
        if (abnormalDTO.getGroupByType() == null) {
            throw new ServiceException("分组类型不能为空");
        }
        if (Func.isNotBlank(abnormalDTO.getReason())) {
            abnormalDTO.setHandlerStatus(1);
        }
        List<Map<String, Object>> datas = null;

        switch (abnormalDTO.getGroupByType()) {
            case 1:  //车场
                datas = countArrearsByParklotId(abnormalDTO);
                break;
            case 2: //原因
                datas = countArrearsByReason(abnormalDTO);
                break;
            case 3:
                datas = countArrearsByDate(abnormalDTO);
                break;
            default:
                throw new ServiceException("统计类型错误！");
        }
        return datas;
    }


    private List<Map<String, Object>> getGroupByField(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> times = new ArrayList<>();
        Map<String, Object> item = null;
        LocalDateTime startTime = LocalDateTime.ofInstant(abnormalDTO.getExitStartTime().toInstant(), ZoneId.systemDefault());
        LocalDateTime endTime = LocalDateTime.ofInstant(abnormalDTO.getExitEndTime().toInstant(), ZoneId.systemDefault());
        long days = ChronoUnit.DAYS.between(startTime, endTime);
        if (days <= 1) {
            abnormalDTO.setDateType(StatementCycleTypeEnum.DAY.getValue());
        } else if (days <= 31) {
            abnormalDTO.setDateType(StatementCycleTypeEnum.MONTH.getValue());
        } else {
            abnormalDTO.setDateType(StatementCycleTypeEnum.YEAR.getValue());
        }
        /**
         * 结束时间大于当前时间取当前时间 过滤掉未来的时间
         */
        if (endTime.isAfter(LocalDateTime.now())) {
            endTime = LocalDateTime.now();
        }
        StatementCycleTypeEnum cycleType = StatementCycleTypeEnum.getType(abnormalDTO.getDateType());
        DateTimeFormatter codeFormatter = null;
        DateTimeFormatter nameFormatter = null;
        switch (cycleType) {
            case DAY:
                codeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
                nameFormatter = DateTimeFormatter.ofPattern("HH:mm");
                while (startTime.isBefore(endTime)) {
                    String code = startTime.format(codeFormatter);
                    String name = startTime.format(nameFormatter);
                    item = new HashMap<>();
                    item.put("code", code);
                    item.put("name", name);
                    item.put("totalNum", 0);
                    item.put("totalAmount", 0.00);
                    item.put("unPaidAmount", 0.00);
                    item.put("paidAmount", 0.00);
                    times.add(item);
                    startTime = startTime.plusHours(1);
                }
                break;
            case MONTH:
                codeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                nameFormatter = DateTimeFormatter.ofPattern("MM-dd");
                while (startTime.isBefore(endTime)) {
                    String code = startTime.format(codeFormatter);
                    String name = startTime.format(nameFormatter);
                    item = new HashMap<>();
                    item.put("code", code);
                    item.put("name", name);
                    item.put("totalNum", 0);
                    item.put("totalAmount", 0.00);
                    item.put("unPaidAmount", 0.00);
                    item.put("paidAmount", 0.00);
                    times.add(item);
                    startTime = startTime.plusDays(1);
                }
                break;
            case YEAR:
                codeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                nameFormatter = DateTimeFormatter.ofPattern("yyyy年MM月");
                while (startTime.isBefore(endTime)) {
                    String code = startTime.format(codeFormatter);
                    String name = startTime.format(nameFormatter);
                    item = new HashMap<>();
                    item.put("code", code);
                    item.put("name", name);
                    item.put("totalNum", 0);
                    item.put("totalAmount", 0.00);
                    item.put("unPaidAmount", 0.00);
                    item.put("paidAmount", 0.00);
                    times.add(item);
                    startTime = startTime.plusMonths(1);
                }
                break;
            default:
                throw new ServiceException("周期类型错误:" + cycleType.getValue());
        }
        return times;
    }

    private List<Map<String, Object>> countArrearsByDate(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> datas = getGroupByField(abnormalDTO);
        List<Map<String, Object>> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.baseMapper.countArrearsByDate(abnormalDTO);
        } else {
            recordList = this.baseMapper.countArrearsByDateDoris(abnormalDTO);
        }
        String enterTime = null;
        String code = "";
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> record : recordList) {
                enterTime = Func.toStr(record.get("enterTime"));
                for (Map<String, Object> data : datas) {
                    code = Func.toStr(data.get("code"));
                    if (enterTime.equals(code)) {
                        data.put("totalNum", record.get("totalNum"));
                        data.put("totalAmount", record.get("totalAmount"));
                        data.put("unPaidAmount", record.get("unPaidAmount"));
                        data.put("paidAmount", record.get("paidAmount"));
                        break;
                    }
                }
            }
        }
        return datas;
    }

    private List<Map<String, Object>> countArrearsByReason(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> recordList = null;
//		abnormalDTO.setHandlerStatus(1);
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.baseMapper.countArrearsByReason(abnormalDTO);
        } else {
            recordList = this.baseMapper.countArrearsByReasonDoris(abnormalDTO);
        }
        return recordList;
    }

    private List<Map<String, Object>> countArrearsByParklotId(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.baseMapper.countArrearsByParklotId(abnormalDTO);
        } else {
            recordList = this.baseMapper.countArrearsByParklotIdDoris(abnormalDTO);
        }
        if (Func.isNotEmpty(recordList)) {
            recordList.sort(Comparator.comparing(map -> (BigDecimal) map.get("unPaidAmount"), Comparator.reverseOrder()));
        }
        return recordList;
    }


}
