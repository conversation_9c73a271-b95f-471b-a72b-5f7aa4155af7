package com.leliven.csc.parking.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.csc.parking.dto.PlaceAbnormalOrderDTO;
import com.leliven.csc.parking.mapper.PlaceAbnormalOrderMapper;
import com.leliven.csc.parking.service.IPlaceAbnormalOrderService;
import com.leliven.csc.parking.vo.PlaceAbnormalOrderVO;

import java.util.List;

import javax.annotation.Resource;

import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springframework.stereotype.Service;

@Service
@TenantIgnore
public class PlaceAbnormalOrderServiceImpl implements IPlaceAbnormalOrderService {

    @Resource
    private PlaceAbnormalOrderMapper placeAbnormalOrderMapper;

    @Override
    public List<PlaceAbnormalOrderVO> tree(PlaceAbnormalOrderDTO dto) {
        return null;
    }

    @Override
    public IPage<PlaceAbnormalOrderVO> pagePlace(IPage<PlaceAbnormalOrderVO> page, PlaceAbnormalOrderDTO dto) {
        return page.setRecords(placeAbnormalOrderMapper.selectPage(page, dto));
    }

    @Override
    public PlaceAbnormalOrderVO detail(Long id) {
        PlaceAbnormalOrderVO vo = placeAbnormalOrderMapper.selectById(id);
        if (vo == null) {
            return null;
        }
        vo.setPresenceParkingOrder(placeAbnormalOrderMapper.selectPresenceParkingOrder(vo.getPlaceId()));
        return vo;
    }

    @Override
    public List<PlaceAbnormalOrderVO> parkingOrders(PlaceAbnormalOrderDTO dto) {
        return placeAbnormalOrderMapper.selectParkingOrders(dto);
    }

    @Override
    public List<PlaceAbnormalOrderVO> captureRecord(PlaceAbnormalOrderDTO dto) {
        return placeAbnormalOrderMapper.selectCaptureRecord(dto);
    }

    @Override
    public List<PlaceAbnormalOrderVO> timeAxis(Long placeId) {
        return placeAbnormalOrderMapper.selectTimeAxis(placeId);
    }

    @Override
    public Boolean finish(PlaceAbnormalOrderDTO dto) {
        return null;
    }

    @Override
    public Boolean enter(PlaceAbnormalOrderDTO dto) {
        return null;
    }

    @Override
    public Boolean exit(PlaceAbnormalOrderDTO dto) {
        return null;
    }

    @Override
    public Boolean refund(PlaceAbnormalOrderDTO dto) {
        return null;
    }

    @Override
    public Boolean remove(PlaceAbnormalOrderDTO dto) {
        return null;
    }
}
