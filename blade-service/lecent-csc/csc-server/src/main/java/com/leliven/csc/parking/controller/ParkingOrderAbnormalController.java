package com.leliven.csc.parking.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.en.parklot.ParkLotType;
import com.lecent.park.vo.ParkingOrderVO;
import com.leliven.csc.common.utils.ParkAuthUtil;
import com.leliven.csc.parking.service.IParkingOrderAbnormalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/1 10:01
 */
@RestController
@RequestMapping("/parking/order/abnormal")
@Api(value = "停车订单", tags = "停车订单")
public class ParkingOrderAbnormalController {

    @Autowired
    private IParkingOrderAbnormalService parkingOrderAbnormalService;

    @GetMapping("/findArrearsDetailList")
    @ApiOperation(value = "欠缴订单")
    public R<IPage<ParkingOrderVO>> findArrearsDetailList(Query query, ParkingOrderDTO parkingOrderDTO) {
        initQueryParam(parkingOrderDTO);
        return R.data(parkingOrderAbnormalService.findArrearsDetailList(Condition.getPage(query), parkingOrderDTO));
    }

    @GetMapping("/countArrearsNum")
    @ApiOperation(value = "欠缴订单总条数")
    public R<Long> countArrearsNum(ParkingOrderDTO parkingOrderDTO) {
        initQueryParam(parkingOrderDTO);
        return R.data(parkingOrderAbnormalService.countArrearsNum(parkingOrderDTO));
    }

    @GetMapping("/findArrearsDetail")
    @ApiOperation(value = "欠缴订单")
    public R<ParkingOrderVO> findArrearsDetail(ParkingOrderDTO parkingOrderDTO) {
        initQueryParam(parkingOrderDTO);
        return R.data(parkingOrderAbnormalService.findArrearsDetail(parkingOrderDTO));
    }

    @PostMapping("/lockOrderErrorHandle")
    @ApiOperation(value = "锁异常处理")
    public R<Boolean> lockOrderErrorHandle(@RequestBody ParkingOrderAbnormalDTO orderAbnormalDTO) {
        return R.data(parkingOrderAbnormalService.lockOrderErrorHandle(orderAbnormalDTO));
    }

    @GetMapping("/countArrears")
    @ApiOperation(value = "图表")
    public R<List<Map<String, Object>>> countArrears(ParkingOrderAbnormalDTO abnormalDTO) {
        initQueryParam(abnormalDTO);
        return R.data(parkingOrderAbnormalService.countArrears(abnormalDTO));
    }

    private static void initQueryParam(ParkingOrderDTO parkingOrderDTO) {
        parkingOrderDTO.setParkLotIds(parkingOrderDTO.mergeParkLotId());
        parkingOrderDTO.setParkLotIds(ParkAuthUtil.parkAuth(parkingOrderDTO.getParkLotIds()));
        parkingOrderDTO.setParklotType(ParkLotType.ROAD_IN.getValue());
        parkingOrderDTO.setPlaceIdList(parkingOrderDTO.mergePlaceIdList());
    }

    private static void initQueryParam(ParkingOrderAbnormalDTO abnormalDTO) {
        abnormalDTO.setParkLotIds(abnormalDTO.mergeParkLotId());
        abnormalDTO.setParkLotIds(ParkAuthUtil.parkAuth(abnormalDTO.getParkLotIds()));
        abnormalDTO.setParklotType(ParkLotType.ROAD_IN.getValue());
        abnormalDTO.setPlaceIdList(abnormalDTO.mergePlaceIdList());
    }
}
