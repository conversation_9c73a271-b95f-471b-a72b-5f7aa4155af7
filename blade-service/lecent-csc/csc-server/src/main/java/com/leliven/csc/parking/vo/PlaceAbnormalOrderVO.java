package com.leliven.csc.parking.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.leliven.csc.parking.entity.PlaceAbnormalOrder;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

/**
 * 车位异常订单VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlaceAbnormalOrderVO extends PlaceAbnormalOrder {

    @ApiModelProperty("停车场名称")
    private String parklotName;

    @ApiModelProperty("车位编号")
    private String placeCode;

    @ApiModelProperty("空闲状态 0-占用 1-空闲")
    private Integer idleState;

    @ApiModelProperty("在场停车记录")
    private PresenceParkingOrder presenceParkingOrder;

    @Data
    public static class PresenceParkingOrder {

        @ApiModelProperty("停车记录id")
        private Long id;

        @ApiModelProperty("车牌")
        private String plate;

        @ApiModelProperty("入场时间")
        private Date enterTime;

        @ApiModelProperty("入场方式")
        private Integer enterWay;

        @ApiModelProperty("入场图片")
        private String enterImgUrl;

        @ApiModelProperty("停车时长")
        private String duration;

        @ApiModelProperty("总金额")
        private BigDecimal totalAmount;

        @ApiModelProperty("已支付金额")
        private BigDecimal paidAmount;

        @ApiModelProperty("优惠金额")
        private BigDecimal discountAmount;

        @ApiModelProperty("未支付金额")
        private BigDecimal unpaidAmount;

        @ApiModelProperty("车辆类型")
        private String carType;
    }
}