package com.leliven.csc.parking.entity;

import java.util.Date;

import org.springblade.core.mp.base.BaseEntity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("c_place_abnormal_order")
@EqualsAndHashCode(callSuper = true)
public class PlaceAbnormalOrder extends BaseEntity {

    @ApiModelProperty("车位ID")
    private Long placeId;

    @ApiModelProperty("停车场ID")
    private Long parklotId;

    @ApiModelProperty("停车记录id")
    private Long parkingId;

    @ApiModelProperty("异常类型")
    private Integer abnormalType;

    @ApiModelProperty("处理人")
    private Long handlerPersonId;

    @ApiModelProperty("处理人")
    private String handlerPersonName;

    @ApiModelProperty("处理时间")
    private Date handlerDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("附件")
    private String attachment;
}
