<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leliven.csc.parking.mapper.PlaceAbnormalOrderMapper">

    <select id="selectPage" resultType="com.leliven.csc.parking.vo.PlaceAbnormalOrderVO">
        SELECT
            *
        FROM
            c_parking_order_abnormal
        WHERE
            is_deleted = 0
        <if test="dto.placeId != null">
            AND place_id = #{dto.placeId}
        </if>
        <if test="dto.parklotId != null">
            AND parklot_id = #{dto.parklotId}
        </if>
        <if test="dto.status != null">
            AND status = #{dto.status}
        </if>
        ORDER BY
            create_time DESC
    </select>

    <select id="selectById" resultType="com.leliven.csc.parking.vo.PlaceAbnormalOrderVO">
        SELECT
            *
        FROM
            c_parking_order_abnormal
        WHERE
            id = #{id}
            AND is_deleted = 0
            AND status = 0
    </select>

    <select id="selectLastParkingOrder" resultType="com.leliven.csc.parking.vo.PlaceAbnormalOrderVO">
        SELECT * FROM c_parking_order WHERE place_id = #{placeId} ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectParkingOrders" resultType="com.leliven.csc.parking.vo.PlaceAbnormalOrderVO">
        SELECT * FROM c_parking_order WHERE place_id = #{placeId} AND exit_time &gt; #{startTime} AND enter_time &lt; #{endTime}
    </select>

    <select id="selectCaptureRecord" resultType="com.leliven.csc.parking.vo.PlaceAbnormalOrderVO">
        SELECT * FROM b_parking_place_camera_capture_record WHERE parking_place_id = #{placeId} AND capture_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="selectTimeAxis" resultType="com.leliven.csc.parking.vo.PlaceAbnormalOrderVO">
        WITH grouped_records AS (
            SELECT
                capture_time,
                plate,
                SUM(change_flag) OVER (
                    ORDER BY capture_time
                ) AS group_seq
            FROM (
                SELECT
                    capture_time,
                    plate,
                    CASE
                        WHEN LAG(plate, 1, NULL) OVER (ORDER BY capture_time) IS NULL THEN 1
                        WHEN plate != LAG(plate, 1, NULL) OVER (ORDER BY capture_time) THEN 1
                        ELSE 0
                    END AS change_flag
                FROM (
                    SELECT
                        capture_time,
                        MAX(plate) AS plate
                    FROM
                        b_parking_place_camera_capture_record
                    WHERE
                        parking_place_id = 1869923016732246024
                        AND type = 3
                    GROUP BY
                        capture_time
                    ORDER BY
                        capture_time DESC
                    LIMIT 1000
                ) t
            ) AS t
        )

        SELECT 
            plate,
            MIN(capture_time) AS start_time,
            MAX(capture_time) AS end_time
        FROM grouped_records
        GROUP BY plate, group_seq
        ORDER BY start_time DESC;
    </select>

    <select id="selectPresenceParkingOrder">
        SELECT
            *
        FROM
            c_parking_order
        WHERE
            is_deleted = 0
            AND parking_status = 2
            AND place_id = #{placeId}
        ORDER BY
            id DESC
        LIMIT 1
    </select>
</mapper>