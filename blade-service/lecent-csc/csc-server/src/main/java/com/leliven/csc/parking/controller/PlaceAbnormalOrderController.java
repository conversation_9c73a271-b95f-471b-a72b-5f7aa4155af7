package com.leliven.csc.parking.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.leliven.csc.parking.dto.PlaceAbnormalOrderDTO;
import com.leliven.csc.parking.service.IPlaceAbnormalOrderService;
import com.leliven.csc.parking.vo.PlaceAbnormalOrderVO;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/place/abnormal/order")
public class PlaceAbnormalOrderController {

    @Resource
    private IPlaceAbnormalOrderService placeAbnormalOrderService;

    @GetMapping("/tree")
    @ApiOperation("树形结构")
    public R<List<PlaceAbnormalOrderVO>> tree(PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.tree(dto));
    }

    @GetMapping("/page")
    @ApiOperation("分页")
    public R<IPage<PlaceAbnormalOrderVO>> page(PlaceAbnormalOrderDTO dto, Query query) {
        return R.data(placeAbnormalOrderService.pagePlace(Condition.getPage(query), dto));
    }

    @GetMapping("/detail")
    @ApiOperation("详情")
    public R<PlaceAbnormalOrderVO> detail(@RequestParam("id") Long id) {
        return R.data(placeAbnormalOrderService.detail(id));
    }

    @GetMapping("/parking-orders")
    @ApiOperation("停车订单")
    public R<List<PlaceAbnormalOrderVO>> parkingOrders(PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.parkingOrders(dto));
    }

    @GetMapping("/capture-record")
    @ApiOperation("抓拍记录")
    public R<List<PlaceAbnormalOrderVO>> captureRecord(PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.captureRecord(dto));
    }

    @GetMapping("/time-axis")
    @ApiOperation("时间轴")
    public R<List<PlaceAbnormalOrderVO>> timeAxis(@RequestParam("placeId") Long placeId) {
        return R.data(placeAbnormalOrderService.timeAxis(placeId));
    }

    @PostMapping("/enter")
    @ApiOperation("入场")
    public R<Boolean> enter(@RequestBody PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.enter(dto));
    }

    @PostMapping("/exit")
    @ApiOperation("出场")
    public R<Boolean> exit(@RequestBody PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.exit(dto));
    }

    @PostMapping("/refund")
    @ApiOperation("退款")
    public R<Boolean> refund(@RequestBody PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.refund(dto));
    }

    @PostMapping("/remove")
    @ApiOperation("删除")
    public R<Boolean> remove(@RequestBody PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.remove(dto));
    }

    @PostMapping("/finish")
    @ApiOperation("完成")
    public R<Boolean> finish(@RequestBody PlaceAbnormalOrderDTO dto) {
        return R.data(placeAbnormalOrderService.finish(dto));
    }
}
