package com.leliven.csc.parking.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.dto.ParkingOrderAbnormalDTO;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.dto.TempParkingOrderDTO;
import com.lecent.park.en.OrderAbnormalTypeEnum;
import com.lecent.park.utils.excel.ExcelUtils;
import com.lecent.park.vo.*;
import com.leliven.csc.alarm.enums.StatementCycleTypeEnum;
import com.leliven.csc.gateway.rpc.parking.dto.ParkingQueryParamDTO;
import com.leliven.csc.parking.service.IParkingRecordService;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.OrderMapper;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderAbnormalMapper;
import com.leliven.park.infrastructure.gateway.persistence.order.mysql.mapper.ParkingOrderMapper;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.entity.KeyValueVo;
import org.springblade.common.excel.ExcelTable;
import org.springblade.common.utils.ActiveProfilesUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023/12/13 14:45
 */
@Slf4j
@Service
public class ParkingRecordServiceImpl implements IParkingRecordService {
    @Resource
    private ParkingOrderAbnormalMapper parkingOrderAbnormalMapper;

    @Resource
    private ParkingOrderMapper parkingOrderMapper;
    @Resource
    private OrderMapper orderMapper;

    /**
     * 获取停车记录列表
     *
     * @param parkingQueryParamDTO 查询参数
     * @return 停车记录列表
     */
    @Override
    public R<IPage<Map<String, Object>>> getParkingRecordList(ParkingQueryParamDTO parkingQueryParamDTO) {
        List<Map<String, Object>> recordList = null;
        ParkingOrderDTO parkingOrderDTO = Func.copy(parkingQueryParamDTO, ParkingOrderDTO.class);
        Query query = Func.copy(parkingQueryParamDTO, Query.class);
        IPage<Map<String, Object>> page = Condition.getPage(query);
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.parkingOrderMapper.getParkingList(page, parkingOrderDTO);
        } else {
            recordList = this.parkingOrderMapper.getParkingListByDoris(page, parkingOrderDTO);
        }
        page.setRecords(recordList);
        return R.data(page);
    }


    /**
     * 查询异常停车记录
     *
     * @param parkingOrderDTO
     * @return
     */
    @Override
    public IPage<ParkingOrderVO> getAbnormalParkingOrderList(IPage<ParkingOrderVO> page, ParkingOrderDTO parkingOrderDTO) {
        List<ParkingOrderVO> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.parkingOrderAbnormalMapper.getAbnormalParkingOrderList(page, parkingOrderDTO);
        } else {
            recordList = this.parkingOrderAbnormalMapper.getAbnormalParkingOrderListByDoris(page, parkingOrderDTO);
        }
        page.setRecords(recordList);
        return page;
    }

    @Override
    public IPage<OrderPageStatisticVO> findTempOrderPage(IPage<OrderPageStatisticVO> page, TempParkingOrderDTO parkingOrder) {
        List<OrderPageStatisticVO> recordList = null;
        recordList = this.orderMapper.findTempOrderPage(page, parkingOrder);
        page.setRecords(recordList);
        return page;
    }

    @Override
    public IPage<ArrearsOrderVO> getArrearsStatisticsList(IPage<ArrearsOrderVO> page, CscCommonQueryParam queryParam) {
        return page.setRecords(orderMapper.getArrearsStatisticsList(page, queryParam));
    }

    @Override
    public IPage<ArrearsOrderVO> countArrearsStatistics(IPage<ArrearsOrderVO> page, CscCommonQueryParam queryParam) {
        List<ArrearsOrderVO> recordList = this.orderMapper.getArrearsStatisticsList(page, queryParam);
        ArrearsOrderVO total = this.orderMapper.countArrearsStatistics(queryParam);
        Optional.ofNullable(total).ifPresent(recordList::add);
        return page.setRecords(recordList);
    }

    @Override
    public List<Map<String, Object>> getArrearsMerchantGroup(CscCommonQueryParam queryParam) {
        if (PlateValidator.isNoPlate(queryParam.getPlate())) {
            return Collections.emptyList();
        }
        return orderMapper.getArrearsMerchantGroup(queryParam);
    }

	@Override
	public IPage<Map<String, Object>> getArrearsDetailList(IPage<Map<String, Object>> page, CscCommonQueryParam commonQueryParam) {
		if(Func.isNotBlank(commonQueryParam.getMerchantId())){
			List<String> merchantIds = Func.toStrList(commonQueryParam.getMerchantId());
			if(merchantIds.size()>1){
				commonQueryParam.setMerchantIds(merchantIds);
				commonQueryParam.setMerchantId(null);
			}
		}
		List<Map<String, Object>> voList = orderMapper.getArrearsDetailList(page, commonQueryParam);
		return page.setRecords(voList);
	}

    @Override
    public List<KeyValueVo> findAbnormalHandlerPersonList() {
        List<KeyValueVo> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.parkingOrderAbnormalMapper.findHandlerPersonList();
        } else {
            recordList = this.parkingOrderAbnormalMapper.findHandlerPersonListByDoris();
        }
        return recordList;
    }

    /**
     * 未处理订单统计
     *
     * @param abnormalDTO
     * @return
     */
    @Override
    public Map<String, Map<String, Object>> countAbnormalOrder(ParkingOrderAbnormalDTO abnormalDTO) {
        abnormalDTO.setGroupByType(1);
        List<Map<String, Object>> recordList = countAbnormalParkingOrder(abnormalDTO);
        Map<String, Map<String, Object>> data = new HashMap<>();
        Integer totalNum = 0;
        Integer abnormalType = null;
        Integer num = null;
        List<Map<String, Object>> items = new ArrayList<>();
        Map<String, Object> item = null;
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> map : recordList) {
                abnormalType = Func.toInt(map.get("abnormalType"));
                num = Func.toInt(map.get("totalNum"));
                totalNum = totalNum + num;
                item = new HashMap<>();
                item.put("name", OrderAbnormalTypeEnum.resolve(abnormalType).getDesc());
                item.put("code", abnormalType);
                item.put("num", num);
                items.add(item);
            }
        }

        Map<String, Object> numMap = new HashMap<>();
        numMap.put("totalNum", totalNum);
        numMap.put("items", items);
        data.put("notHandlerNum", numMap);
        return data;
    }

    private List<Map<String, Object>> countAbnormalParkingOrder(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> recordList = null;
        if (ActiveProfilesUtil.isTest()) {
            recordList = this.parkingOrderAbnormalMapper.countAbnormalParkingOrder(abnormalDTO);
        } else {
            recordList = this.parkingOrderAbnormalMapper.countAbnormalParkingOrderByDoris(abnormalDTO);
        }
        return recordList;
    }


    /**
     * 统计已处理的订单
     *
     * @param abnormalDTO
     * @return
     */
    @Override
    public Map<String, Map<String, Object>> countAbnormalHandlerOrder(ParkingOrderAbnormalDTO abnormalDTO) {
        abnormalDTO.setGroupByType(1);
        abnormalDTO.setStatus(1);
        List<Map<String, Object>> recordItems = countAbnormalHandlerOrderGroup(abnormalDTO);
        Map<String, Map<String, Object>> data = new HashMap<>();
        Integer totalNum = 0;
        Double totalAmount = 0.0;
        Double paidAmount = 0.0;
        Double unpaidAmount = 0.0;
        Double commission = 0.0;
        List<Map<String, Object>> numItems = new ArrayList<>();
        Map<String, Object> numItem = null;
        if (Func.isNotEmpty(recordItems)) {
            for (Map<String, Object> map : recordItems) {
                Integer abnormalType = Func.toInt(map.get("abnormalType"));
                Integer num = Func.toInt(map.get("totalNum"));
                numItem = new HashMap<>();
                numItem.put("name", OrderAbnormalTypeEnum.resolve(abnormalType).getDesc());
                numItem.put("code", abnormalType);
                numItem.put("num", num);
                numItems.add(numItem);
                totalNum = totalNum + num;
                Double amountItem = Func.toDouble(map.get("totalAmount"));
                totalAmount = amountItem + totalAmount;
                Double paidAmountItem = Func.toDouble(map.get("paidAmount"));
                paidAmount = paidAmountItem + paidAmount;
                Double unpaidAmountItem = Func.toDouble(map.get("unpaidAmount"));
                unpaidAmount = unpaidAmountItem + unpaidAmount;
                Double commissionItem = Func.toDouble(map.get("commission"));
                commission = commissionItem + commission;
            }
        }
        /**
         * 条数
         */
        Map<String, Object> numMap = new HashMap<>(2);
        data.put("handlerNum", numMap);
        numMap.put("totalNum", totalNum);
        numMap.put("items", numItems);
        /**
         * 总金额
         */
        Map<String, Object> amountMap = new HashMap<>(3);
        data.put("amount", amountMap);
        amountMap.put("totalAmount", doubleToString(totalAmount));
        amountMap.put("unpaidAmount", doubleToString(unpaidAmount));
        amountMap.put("paidAmount", doubleToString(paidAmount));
        /**
         * 佣金
         */
        Map<String, Object> commissionMap = new HashMap<>(1);
        data.put("commission", commissionMap);
        commissionMap.put("commission", doubleToString(commission));
        /**
         * 已删除
         */
        abnormalDTO.setGroupByType(null);
        abnormalDTO.setStatus(2);
        recordItems = countAbnormalHandlerOrderGroup(abnormalDTO);
        Integer delNum = 0;
        if (Func.isNotEmpty(recordItems)) {
            delNum = Func.toInt(recordItems.get(0).get("totalNum"));
        }
        Map<String, Object> delMap = new HashMap<>(1);
        data.put("delNum", delMap);
        delMap.put("delNum", delNum);

        /**
         * 查询异常条数
         */
        ParkingOrderAbnormalDTO param = Func.copy(abnormalDTO, ParkingOrderAbnormalDTO.class);
        param.setAbnormalType(null);
        param.setGroupByType(1);
        param.setEnterStartTime(abnormalDTO.getHandlerDateStart());
        param.setEnterEndTime(abnormalDTO.getHandlerDateEnd());
        param.setHandlerDateStart(null);
        param.setHandlerDateEnd(null);
        param.setHandlerPersonId(null);
        param.setStatus(null);
        //未处理条数
        Map<String, Map<String, Object>> notHandlerMap = countAbnormalOrder(param);
        Map<String, Object> abnormalMap = notHandlerMap.get("notHandlerNum");
        totalNum = Func.toInt(abnormalMap.get("totalNum"));
        List<Map<String, Object>> items = (List<Map<String, Object>>) abnormalMap.get("items");
        //已处理条数
        recordItems = countAbnormalHandlerOrderGroup(param);
        boolean b = false;
        if (Func.isNotEmpty(recordItems)) {
            for (Map<String, Object> map : recordItems) {
                Integer abnormalType = Func.toInt(map.get("abnormalType"));
                Integer num = Func.toInt(map.get("totalNum"));
                totalNum = totalNum + num;
                b = true;
                for (Map<String, Object> item : items) {
                    if (abnormalType.equals(Func.toInt(item.get("code").toString()))) {
                        num = Func.toInt(item.get("num")) + num;
                        item.put("num", num);
                        b = false;
                        break;
                    }
                }
                if (b) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", OrderAbnormalTypeEnum.resolve(abnormalType).getDesc());
                    item.put("code", abnormalType);
                    item.put("num", num);
                    items.add(item);
                }
            }
        }
        abnormalMap.put("totalNum", totalNum);
        abnormalMap.put("items", items);
        data.put("abnormalNum", abnormalMap);
        return data;
    }

    private String doubleToString(double d) {
        DecimalFormat df = new DecimalFormat("#.00");
        String formattedNumber = df.format(d);
        double result = Double.parseDouble(formattedNumber);
        return String.valueOf(result);
    }

    private List<Map<String, Object>> countAbnormalHandlerOrderGroup(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> recordList = null;
        List<Map<String, Object>> recordList1 = null;
//		if (ActiveProfilesUtil.isTest()) {
//			recordList = this.parkingOrderAbnormalMapper.countAbnormalHandlerOrder(abnormalDTO);
//			recordList1 = this.parkingOrderAbnormalMapper.countAbnormalHandlerOrderPaidAmount(abnormalDTO);
//		} else {
        recordList = this.parkingOrderAbnormalMapper.countAbnormalHandlerOrderByDoris(abnormalDTO);
        recordList1 = this.parkingOrderAbnormalMapper.countAbnormalHandlerOrderPaidAmountByDoris(abnormalDTO);
//		}
        //合并
        if (Func.isNotEmpty(recordList) && Func.isNotEmpty(recordList1)) {
            for (Map<String, Object> map1 : recordList) {
                if (map1 == null) {
                    continue;
                }
                String key1 = getDataKey(abnormalDTO.getGroupByType(), map1);
                for (Map<String, Object> map2 : recordList1) {
                    if (map2 == null) {
                        continue;
                    }
                    String key2 = getDataKey(abnormalDTO.getGroupByType(), map2);
                    if (key1 == null || key1.equals(key2)) {
                        map1.put("paidAmount", map2.get("paidAmount"));
                        map1.put("commission", map2.get("commission"));
                        recordList1.remove(map2);
                        break;
                    }
                }
            }
            if (recordList1.size() > 0) {
                for (Map<String, Object> map2 : recordList1) {
                    recordList.add(map2);
                }
            }


        } else if (Func.isNotEmpty(recordList1)) {
            recordList = recordList1;
        }
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> map1 : recordList) {
                if (map1 == null) {
                    continue;
                }
                if (!map1.containsKey("totalNum")) {
                    map1.put("totalNum", 0);
                }
                if (!map1.containsKey("totalAmount")) {
                    map1.put("totalAmount", 0);
                }
                if (!map1.containsKey("paidAmount")) {
                    map1.put("paidAmount", 0);
                }
                if (!map1.containsKey("unpaidAmount")) {
                    map1.put("unpaidAmount", 0);
                }
                if (!map1.containsKey("commission")) {
                    map1.put("commission", 0);
                }
            }
        }
        return recordList;
    }


    private String getDataKey(Integer groupByType, Map<String, Object> map) {
        if (groupByType == null) {
            return null;
        }
        switch (groupByType) {
            case 1:
                return map.get("abnormalType").toString();
            case 2:
                return map.get("handlerPersonId").toString();
            case 3:
                return map.get("handlerDate").toString();
            case 4:
                return map.get("enterTime").toString();
        }
        return null;
    }

    /**
     * 图表
     *
     * @param abnormalDTO
     * @return
     */
    @Override
    public List<Map<String, Object>> countAbnormalHandlerOrderByChart(ParkingOrderAbnormalDTO abnormalDTO) {
        if (abnormalDTO.getGroupByType() == null) {
            throw new ServiceException("分组类型不能为空");
        }
        List<Map<String, Object>> datas = null;

        switch (abnormalDTO.getGroupByType()) {
            case 1:
                datas = countAbnormalHandlerOrderByAbnormalType(abnormalDTO);
                break;
            case 2:
                datas = countAbnormalHandlerOrderByHandlerPersonId(abnormalDTO);
                break;
            case 3:
                datas = countAbnormalHandlerOrderByDate(abnormalDTO);
                break;
            default:
                throw new ServiceException("统计类型错误！");
        }
        return datas;
    }

    /**
     * 根据差异类型分组
     *
     * @param abnormalDTO
     * @return
     */
    private List<Map<String, Object>> countAbnormalHandlerOrderByAbnormalType(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> recordList = countAbnormalHandlerOrderGroup(abnormalDTO);
        Integer abnormalType = null;
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> record : recordList) {
                abnormalType = Func.toInt(record.get("abnormalType"));
                record.put("code", abnormalType);
                record.put("name", OrderAbnormalTypeEnum.resolve(abnormalType).getDesc());
                record.remove("abnormalType");
                record.put("handlerNum", record.get("totalNum"));
                record.remove("totalNum");
            }
        }
        return recordList;
    }

    /**
     * 根据处理人分组
     *
     * @param abnormalDTO
     * @return
     */
    private List<Map<String, Object>> countAbnormalHandlerOrderByHandlerPersonId(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> recordList = countAbnormalHandlerOrderGroup(abnormalDTO);
        String handlerPersonName = null;
        String handlerPersonId = null;

        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> record : recordList) {
                handlerPersonName = Func.toStr(record.get("handlerPersonName"));
                handlerPersonId = Func.toStr(record.get("handlerPersonId"));
                record.put("code", handlerPersonId);
                record.put("name", handlerPersonName);
                record.remove("handlerPersonName");
                record.remove("handlerPersonId");
                record.put("handlerNum", record.get("totalNum"));
                record.remove("totalNum");
            }
        }
        return recordList;
    }

    private List<Map<String, Object>> countAbnormalHandlerOrderByDate(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> datas = getGroupByField(abnormalDTO);
        abnormalDTO.setGroupByType(3);
        /**
         * 已处理
         */
        List<Map<String, Object>> recordList = countAbnormalHandlerOrderGroup(abnormalDTO);
        String handlerDate = null;
        String code = "";
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> record : recordList) {
                handlerDate = Func.toStr(record.get("handlerDate"));
                for (Map<String, Object> data : datas) {
                    code = Func.toStr(data.get("code"));
                    if (handlerDate.equals(code)) {
                        data.put("handlerNum", record.get("totalNum"));
                        data.put("totalAmount", record.get("totalAmount"));
                        data.put("unpaidAmount", record.get("unpaidAmount"));
                        data.put("paidAmount", record.get("paidAmount"));
                        break;
                    }
                }
            }
        }
        /**
         * 异常订单数  未处理+已处理
         */
        ParkingOrderAbnormalDTO param = Func.copy(abnormalDTO, ParkingOrderAbnormalDTO.class);
        param.setEnterStartTime(abnormalDTO.getHandlerDateStart());
        param.setEnterEndTime(abnormalDTO.getHandlerDateEnd());
        param.setGroupByType(4);
        param.setHandlerDateStart(null);
        param.setHandlerDateEnd(null);
        recordList = countAbnormalParkingOrder(param);
        String enterDate = null;
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> record : recordList) {
                enterDate = Func.toStr(record.get("enterTime"));
                for (Map<String, Object> data : datas) {
                    code = Func.toStr(data.get("code"));
                    if (enterDate.equals(code)) {
                        data.put("abnormalNum", record.get("totalNum"));
                        break;
                    }
                }
            }
        }
        //已处理

        Integer abnormalNum = null;
        Integer handlerAbnormalNum = null;
        recordList = countAbnormalHandlerOrderGroup(param);
        if (Func.isNotEmpty(recordList)) {
            for (Map<String, Object> record : recordList) {
                handlerDate = Func.toStr(record.get("enterTime"));
                for (Map<String, Object> data : datas) {
                    code = Func.toStr(data.get("code"));
                    if (handlerDate.equals(code)) {
                        abnormalNum = Func.toInt(data.get("abnormalNum"));
                        handlerAbnormalNum = Func.toInt(record.get("totalNum"));
                        data.put("abnormalNum", abnormalNum + handlerAbnormalNum);
                        break;
                    }
                }
            }
        }
        return datas;
    }


    public List<Map<String, Object>> getGroupByField(ParkingOrderAbnormalDTO abnormalDTO) {
        List<Map<String, Object>> times = new ArrayList<>();
        Map<String, Object> item = null;
        LocalDateTime startTime = LocalDateTime.ofInstant(abnormalDTO.getHandlerDateStart().toInstant(), ZoneId.systemDefault());
        LocalDateTime endTime = LocalDateTime.ofInstant(abnormalDTO.getHandlerDateEnd().toInstant(), ZoneId.systemDefault());
        if (StatementCycleTypeEnum.CUSTOM.getValue().equals(abnormalDTO.getDateType())) {
            long days = ChronoUnit.DAYS.between(startTime, endTime);
            if (days <= 1) {
                abnormalDTO.setDateType(StatementCycleTypeEnum.DAY.getValue());
            } else if (days <= 31) {
                abnormalDTO.setDateType(StatementCycleTypeEnum.MONTH.getValue());
            } else {
                abnormalDTO.setDateType(StatementCycleTypeEnum.YEAR.getValue());
            }
        }
        /**
         * 结束时间大于当前时间取当前时间 过滤掉未来的时间
         */
        if (endTime.isAfter(LocalDateTime.now())) {
            endTime = LocalDateTime.now();
        }
        StatementCycleTypeEnum cycleType = StatementCycleTypeEnum.getType(abnormalDTO.getDateType());
        DateTimeFormatter codeFormatter = null;
        DateTimeFormatter nameFormatter = null;
        switch (cycleType) {
            case DAY:
                codeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
                nameFormatter = DateTimeFormatter.ofPattern("HH:mm");
                while (startTime.isBefore(endTime)) {
                    String code = startTime.format(codeFormatter);
                    String name = startTime.format(nameFormatter);
                    item = new HashMap<>();
                    item.put("code", code);
                    item.put("name", name);
                    item.put("abnormalNum", 0);
                    item.put("handlerNum", 0);
                    item.put("totalAmount", 0.00);
                    item.put("unpaidAmount", 0.00);
                    item.put("paidAmount", 0.00);
                    times.add(item);
                    startTime = startTime.plusHours(1);
                }
                break;
            case MONTH:
                codeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                nameFormatter = DateTimeFormatter.ofPattern("MM-dd");
                while (startTime.isBefore(endTime)) {
                    String code = startTime.format(codeFormatter);
                    String name = startTime.format(nameFormatter);
                    item = new HashMap<>();
                    item.put("code", code);
                    item.put("name", name);
                    item.put("abnormalNum", 0);
                    item.put("handlerNum", 0);
                    item.put("totalAmount", 0.00);
                    item.put("unpaidAmount", 0.00);
                    item.put("paidAmount", 0.00);
                    times.add(item);
                    startTime = startTime.plusDays(1);
                }
                break;
            case YEAR:
                codeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
                nameFormatter = DateTimeFormatter.ofPattern("yyyy年MM月");
                while (startTime.isBefore(endTime)) {
                    String code = startTime.format(codeFormatter);
                    String name = startTime.format(nameFormatter);
                    item = new HashMap<>();
                    item.put("code", code);
                    item.put("name", name);
                    item.put("abnormalNum", 0);
                    item.put("handlerNum", 0);
                    item.put("totalAmount", 0.00);
                    item.put("unpaidAmount", 0.00);
                    item.put("paidAmount", 0.00);
                    times.add(item);
                    startTime = startTime.plusMonths(1);
                }
                break;
            default:
                throw new ServiceException("周期类型错误:" + cycleType.getValue());
        }
        return times;
    }

    /**
     * 导出欠费统计数据
     *
     * @param response         HTTP响应，用于将Excel文件作为响应体返回给客户端
     * @param commonQueryParam 普通查询参数，封装了前端传来的各种查询条件
     */
    @Override
    public void exportArrearsDetailList(HttpServletResponse response, CscCommonQueryParam commonQueryParam) {
        commonQueryParam.setTenantId(AuthUtil.getTenantId());
        // 查询欠费统计信息列表
        List<ArrearsOrderVO> list = orderMapper.getArrearsStatisticsList(null, commonQueryParam);
        List<ArrearsOrderExcel> excels = BeanUtil.copy(list, ArrearsOrderExcel.class);
        ArrearsOrderVO total = orderMapper.countArrearsStatistics(commonQueryParam);

        org.springblade.common.excel.ExcelUtils.export(response, ExcelTable.<ArrearsOrderExcel>builder()
            .title("欠费统计")
            .data(excels)
            .total(BeanUtil.copy(total, ArrearsOrderExcel.class))
            .build());
    }

	/**
	 * 导出欠费详情数据
	 *
	 * @param response         HttpServletResponse对象，用于响应客户端请求
	 * @param commonQueryParam 查询参数对象，封装了前端传来的各种查询条件
	 */
	@Override
	public void exportArrearsStatisticsList(HttpServletResponse response, CscCommonQueryParam commonQueryParam) {
		log.info("exportArrearsStatisticsList :"+Func.toJson(commonQueryParam));
		if(Func.isNotBlank(commonQueryParam.getMerchantId())){
		    List<String> merchantIds = Func.toStrList(commonQueryParam.getMerchantId());
			if(merchantIds.size()>1){
				commonQueryParam.setMerchantIds(merchantIds);
				commonQueryParam.setMerchantId(null);
			}
		}

		// 查询欠费详情列表
		List<Map<String, Object>> page = orderMapper.getArrearsDetailList(null, commonQueryParam);
		JSONArray jsonArray = new JSONArray();
		jsonArray.addAll(page);
		List<ArrearsOrderDetailVO> list = jsonArray.toJavaList(ArrearsOrderDetailVO.class);

		org.springblade.common.excel.ExcelUtils.export(response, ExcelTable.<ArrearsOrderDetailVO>builder()
			.title("订单欠费详情")
			.data(list)
			.build());
	}
}
