package com.leliven.park.application.parking;

import com.lecent.park.entity.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.RoadSideParkingDTO;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotDeviceRetVO;

import java.util.Date;
import java.util.List;

/**
 * 路内停车业务
 * <AUTHOR>
 */
public interface IRoadSideParkingBiz {

	/**
	 * 路边停车设备触发车辆进出场
	 *
	 * @param parkingDTO 路内停车请求体
	 * @return ChannelTodoVO
	 */
	ChannelTodoVO roadSideDeviceTrigger(RoadSideParkingDTO parkingDTO);

	ChannelTodoVO roadSideParking(RoadSideParkingDTO req, ParkingPlace place);

	/**
	 * 根据停车记录创建代办
	 * 创建代办
	 * @param parkingOrder 停车记录
	 * @return {@link ChannelTodo}
	 */
	ChannelTodo todoParkingDetail(ParkingOrder parkingOrder);
	/**
	 * 查询停车待缴订单
	 *
	 * @param place     车位
	 * @param parkingId 停车id
	 * @return {@link ParkingDetail}
	 */
	ParkingDetail todoParkingDetail(ParkingPlace place, Long parkingId);

	/**
	 * 查询停车待缴订单
	 *
	 * @param place     车位
	 * @param parkingId 停车id
	 * @return {@link ParkingDetail}
	 */
	ParkingDetail todoParkingDetailV2(ParkingPlace place, Long parkingId);

	/**
	 * 过滤违规停车
	 *
	 * @param parkingDTO 停车dto
	 * @return boolean
	 */
	boolean filterViolationParking(RoadSideParkingDTO parkingDTO);

	/**
	 * 计算在场车辆的费用
	 *
	 * @param placeId   车位id
	 * @param plate     车牌
	 * @param parklotId 停车场 ID
	 * @return {@link ChannelTodo }
	 */
	ChannelTodo todoParkingDetail(Long placeId, String plate, Long parklotId);

	/**
	 * 查询设备绑定的详细信息
	 *
	 * @param placeId 车位id
	 * @return {@link List }<{@link ParklotDeviceRetVO }>
	 */
	List<ParklotDeviceRetVO> findParklotDeviceInfoByPlaceId(Long placeId);

	PlateProperty getPlateProperty(Long parklotId, String plate, Date enterTime, Date exitTime);

	/**
	 * 处理频繁进出场的订单合并
	 * 若{{oftenEnterExitGapTime}}分钟内多次进出场，则将前一条置为在场，新的进出抛弃
	 *
	 * @param req 请求参数
	 * @param place 车位信息
	 * @return 是否需要合并订单（true表示已合并，无需继续处理）
	 */
	ParkingOrder mergeFrequentEnterExitOrder(RoadSideParkingDTO req, ParkingPlace place);
}
