package com.lecent.park.biz.impl;

import cn.hutool.core.comparator.CompareUtil;
import com.lecent.device.dto.DeviceParkingForwardEventDTO;
import com.lecent.device.enums.ParkSpaceStatus;
import com.lecent.park.access.ParkAccessClient;
import com.lecent.park.access.ParkTodoContext;
import com.lecent.park.cache.ParkCacheNames;
import com.lecent.park.common.cache.ParkLotCaches;
import com.lecent.park.common.utils.PlateCheckUtils;
import com.lecent.park.constant.ChannelConstant;
import com.lecent.park.core.mq.rabbitmq.MessageConstant;
import com.lecent.park.core.mq.rabbitmq.MqSender;
import com.lecent.park.core.mq.rabbitmq.exchange.Exchanges;
import com.lecent.park.core.mq.rabbitmq.routing.RoutingKeys;
import com.lecent.park.dto.ParkingOrderDTO;
import com.lecent.park.dto.ViolationRecordDTO;
import com.lecent.park.en.EnableStatus;
import com.lecent.park.en.PassTemplate;
import com.lecent.park.en.channeltodo.ChannelWay;
import com.lecent.park.en.channeltodo.ChargeEnum;
import com.lecent.park.en.channeltodo.TypeEnum;
import com.lecent.park.en.parklot.ParkingStatusEnum;
import com.lecent.park.en.temporder.CreateWay;
import com.lecent.park.en.unpaidorder.UnpaidOrderType;
import com.lecent.park.entity.*;
import com.lecent.park.event.channeltodo.ParkChannelMessageEvent;
import com.lecent.park.service.*;
import com.lecent.park.vo.ChannelTodoVO;
import com.lecent.park.vo.RoadSideParkingDTO;
import com.leliven.device.domain.shared.valueobject.DeviceType;
import com.leliven.park.application.parking.IRoadSideParkingBiz;
import com.leliven.park.application.parking.ParkingErrorAppService;
import com.leliven.park.application.parking.RoadsideParkingAppServiceI;
import com.leliven.park.application.parking.assembler.ParkingImageAssembler;
import com.leliven.park.application.vehicle.assembler.VehicleAssembler;
import com.leliven.park.common.model.valueobject.BasicParkingScene;
import com.leliven.park.common.model.valueobject.ParkingTriggerWay;
import com.leliven.park.domain.basic.device.gateway.DeviceGatewayI;
import com.leliven.park.domain.basic.payway.ParklotPayMerchantRepositoryI;
import com.leliven.park.domain.basic.payway.entity.ParklotPayMerchantManager;
import com.leliven.park.domain.basic.place.model.valueobject.ParkingPlaceIdleState;
import com.leliven.park.domain.order.parking.entity.PlaceParkingEventInfo;
import com.leliven.park.domain.parking.core.ParkingRepositoryI;
import com.leliven.park.domain.parking.core.model.ParkingEventDiscardRuleConfig;
import com.leliven.park.domain.parking.core.model.objectvalue.RoadsideParkingLockCtrlSceneParam;
import com.leliven.park.domain.parking.core.support.RoadsideParkingLockCtrlProcessor;
import com.leliven.park.domain.parking.core.support.RoadsideParkingRepeatEnterEventHandlerI;
import com.leliven.park.domain.parking.image.ParkingImageRepositoryI;
import com.leliven.park.domain.parking.image.factory.ParkingImageFactory;
import com.leliven.park.domain.vehicle.support.VehicleDomainService;
import com.leliven.park.infrastructure.gateway.persistence.basic.vo.ParklotDeviceRetVO;
import com.leliven.park.infrastructure.gateway.persistence.vehicle.converter.VehicleConverter;
import com.leliven.vehicle.model.Vehicle;
import com.leliven.vehicle.validator.PlateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CacheUtils;
import org.springblade.common.utils.LecentAssert;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.lecent.park.cache.ParkCacheNames.TEMP_PARKING_ORDER_PAY_LOCK;

/**
 * 路内停车业务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RoadSideParkingBiz implements IRoadSideParkingBiz {

	@Resource
	private IRoadSideParkingBiz self;
	@Resource
	private RedisLockClient redisLockClient;
	@Resource
	private IParkingPlaceService placeService;
	@Resource
	private ParkAccessClient parkAccessClient;
	@Resource
	private IChannelTodoService channelTodoService;
	@Resource
	private IParkingOrderService parkingOrderService;
	@Resource
	private ITempParkingChargeRuleService tempParkingChargeRuleService;
	@Resource
	private DeviceGatewayI deviceGateway;
	@Resource
	private IParklotDeviceRetService parklotDeviceRetService;
	@Resource
	private IPlatePropertyService platePropertyService;
	@Resource
	private VehicleDomainService vehicleDomainService;
	@Resource
	private VehicleAssembler vehicleAssembler;
	@Resource
	private ParkingImageRepositoryI parkingImageRepository;
	@Resource
	private RoadsideParkingRepeatEnterEventHandlerI roadsideParkingRepeatEnterEventHandler;
	@Resource
	private ITempParkingUnpaidOrderService unpaidOrderService;
	@Resource
	private ITempParkingOrderService orderService;
	@Resource
	private RoadsideParkingAppServiceI roadsideParkingAppService;
	@Lazy
	@Resource
	private ParkingErrorAppService parkingErrorAppService;
	@Resource
	private ParkingRepositoryI parkingRepository;
	@Resource
	private ParklotPayMerchantRepositoryI parklotPayMerchantRepository;
	@Resource
	private MqSender sender;
	@Resource
	private IViolationRecordService violationRecordService;
	@Resource
	private RoadsideParkingLockCtrlProcessor lockCtrlProcessor;
	@Resource
	private IVehicleService vehicleService;


	@Override
	public ChannelTodoVO roadSideDeviceTrigger(RoadSideParkingDTO parkingDTO) {
		ParkingPlace place;
		if (Func.isNotBlank(parkingDTO.getSn())) {
			place = placeService.existByDeviceSn(parkingDTO.getSn());
		} else if (parkingDTO.getPlaceId() != null) {
			place = placeService.getById(parkingDTO.getPlaceId());
		} else if (parkingDTO.getPayCode() != null) {
			place = placeService.detailByPayCode(parkingDTO.getPayCode());
		} else {
			throw new ServiceException("参数[sn]或[placeId]不能为空");
		}

		parkingDTO.setPlaceId(place.getId());
		parkingDTO.setPayCode(place.getPayCode());
		parkingDTO.setPlaceCode(place.getPlaceCode());
		parkingDTO.setParklotId(place.getParklotId());
		// 车位暂停使用后，车位不再计费
		if (EnableStatus.INVALID.getCode().equals(place.getStatus())) {
			log.info("roadSideDeviceTrigger 车位[{}]暂停使用，不再计费", place.getPlaceCode());
			return null;
		}
		// 过滤地磁触发
		if (filterGeomagneticTrigger(parkingDTO, place)) {
			log.info("过滤车场[{}]的地磁触发", place.getParklotId());
			return null;
		}
		if (filterViolationParking(parkingDTO)) {
			log.info("过滤违规停车车辆[{}]", parkingDTO.getPlate());
			return null;
		}

		ParkingPlace finalPlace = place;
		ChannelTodoVO channelTodoVO = redisLockClient.lockFair(
			ParkCacheNames.LECENT_PARK_PLACE_PARKING_TRIGGER_LOCK + place.getPayCode(),
			10L,
			15L,
			() -> self.roadSideParking(parkingDTO, finalPlace));
		// 保存停车图片
		saveParkingImages(channelTodoVO, parkingDTO);
		// 接收到出场事件，降锁
		parkingExitAsyncUnlock(parkingDTO);
		// 视频桩出入场消息推送至device服务
		parkingEventForward(parkingDTO);

		return channelTodoVO;
	}

	@Override
	public boolean filterViolationParking(RoadSideParkingDTO parkingDTO) {
		if (ChannelWay.WAY_12.getValue().equals(parkingDTO.getTriggerType()) &&
			parkingRepository.getViolationParklots().contains(parkingDTO.getParklotId())) {
			ViolationRecordDTO violationRecordDTO = new ViolationRecordDTO();
			violationRecordDTO.setPlate(parkingDTO.getPlate());
			violationRecordDTO.setParklotId(parkingDTO.getParklotId());
			violationRecordDTO.setPlaceId(parkingDTO.getPlaceId());
			violationRecordDTO.setDate(parkingDTO.getDate());
			violationRecordDTO.setImageUrl(
				Optional.ofNullable(parkingDTO.getImageUrlMap())
					.map(Map::values)
					.orElseGet(ArrayList::new)
					.stream()
					.map(Func::join)
					.collect(Collectors.joining(",")));
			violationRecordDTO.setTriggerType(parkingDTO.getTriggerType());
			violationRecordDTO.setIsEnter(parkingDTO.isEnter());
			violationRecordService.createViolationRecord(violationRecordDTO);
			return true;
		} else {
			return false;
		}
	}

	private boolean filterGeomagneticTrigger(RoadSideParkingDTO parkingDTO, ParkingPlace place) {
		if (DeviceType.MAGNETIC.getValue().equals(parkingDTO.getDeviceType())) {
			List<Long> filterParklots = parkingRepository.filteredGeomagneticTriggeredParklots();
			return filterParklots.contains(place.getParklotId());
		}
		return false;
	}

	private boolean filterLockTrigger(RoadSideParkingDTO parkingDTO, ParkingPlace place) {
		if (!DeviceType.PARKING_LOCK.getValue().equals(parkingDTO.getDeviceType())) {
			return false;
		}

		ParkingEventDiscardRuleConfig discardRuleConfig = parkingRepository.getParkingEventDiscardRuleConfig(ChannelWay.WAY_5);
		if (Objects.nonNull(discardRuleConfig) && discardRuleConfig.isEnabled()) {
			if (discardRuleConfig.includedInAny(place.getPayCode(), place.getParklotId(), place.getTenantId())) {
				return true;
			}
		}

		// 车位锁进场触发redis key
		String key = ParkCacheNames.PARKING_LOCK_ENTER_TRIGGER + place.getId();
		// 缓存时间
		long cacheTime = 1;

		if (parkingDTO.isEnter()) {
			// 从缓存获取车位锁进场状态，如果为false，则过滤掉，默认为true
			boolean enterStatus = Func.toBoolean(CacheUtils.get(key), Boolean.TRUE);
			if (!enterStatus) {
				log.info("车位[{}]在延迟处理期间有出场触发，忽略本次进场触发", place.getId());
				// 清除缓存
				CacheUtils.delKey(key);
				return true;
			}
		} else {
			// 出场时如果该车位有进场缓存，这将缓存置为false，并抛弃本次出场触发
			if (CacheUtils.exists(key)) {
				CacheUtils.setEx(key, Boolean.FALSE, cacheTime);
				log.info("车位[{}]有进场缓存，忽略本次出场触发", place.getId());
				return true;
			}
		}

		// 查询停车记录
		ParkingOrder parkingOrder = parkingOrderService.queryLastParkingOrderByPlace(place.getId());
		boolean delayHandle = Boolean.TRUE.equals(parkingDTO.getDelayHandle());

		if (parkingOrder != null) {
			if (delayHandle && (parkingOrder.getEnterTime().after(parkingDTO.getDate())
				|| (ParkingStatusEnum.isExited(parkingOrder.getParkingStatus())
				&& parkingOrder.getExitTime().after(parkingDTO.getDate())))) {
				log.info("车位[{}]在延迟处理期间有新停车记录[{}]，忽略本次触发", place.getId(), parkingOrder.getId());
				return true;
			}
			if (ParkingStatusEnum.isPresent(parkingOrder.getParkingStatus())) {
				if (parkingDTO.isEnter()) {
					log.info("车位[{}]查询到在场停车记录，忽略本次进场触发", place.getId());
					return true;
				} else if (delayHandle) {
					log.info("车位[{}]延迟30分钟降锁", place.getId());
					sender.sendDelayMessage(Func.toJson(parkingDTO), parkingRepository.getUnlockDelayTime(),
						Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE, RoutingKeys.LECENT_PARK_UNLOCK);
					return true;
				}
			} else if (!parkingDTO.isEnter()) {
				log.info("车位[{}]未查询到在场停车记录，忽略本次出场触发", place.getId());
				return true;
			}
		}

		// 延迟处理进出场触发
		if (!delayHandle) {
			log.info("延迟处理车位锁进出场触发");
			parkingDTO.setDelayHandle(true);
			sender.sendDelayMessage(Func.toJson(parkingDTO), parkingRepository.getParkLockTriggeredDelayTime(),
				Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE, RoutingKeys.LECENT_PARK_MAGNETIC_ENTER);
			// 缓存进场触发
			if (parkingDTO.isEnter()) {
				CacheUtils.setEx(key, Boolean.TRUE, cacheTime);
			}
			return true;
		}
		return false;
	}

	private void parkingExitAsyncUnlock(RoadSideParkingDTO parkingDTO) {
		if (!parkingDTO.isEnter()
			&& !DeviceType.MAGNETIC.getValue().equals(parkingDTO.getDeviceType())
			&& !DeviceType.PARKING_LOCK.getValue().equals(parkingDTO.getDeviceType())) {
			this.lockCtrlProcessor.asyncUnlock(
				new RoadsideParkingLockCtrlSceneParam(parkingDTO.getPlaceId(), BasicParkingScene.VEHICLE_EXITED));
		}
	}

	private void parkingEventForward(RoadSideParkingDTO parkingDTO) {
		// 非视频桩触发的停车事件，直接结束
		if (!DeviceType.VIDEO_PILE.getValue().equals(parkingDTO.getDeviceType())) {
			return;
		}

		Optional.ofNullable(parklotDeviceRetService.getOneParkingLockByPlaceId(parkingDTO.getPlaceId()))
			.ifPresent(ret -> this.deviceGateway.asyncForwardParkingEvent(
					DeviceParkingForwardEventDTO.builder()
						.targetSn(ret.getDeviceSn())
						.sourceSn(parkingDTO.getSn())
						.triggerTime(parkingDTO.getDate())
						.plate(parkingDTO.getPlate())
						.parkSpaceStatus(parkingDTO.isEnter() ? ParkSpaceStatus.USED : ParkSpaceStatus.IDLE)
						.build()
				)
			);
	}

	private void saveParkingImages(ChannelTodoVO channelTodoVO, RoadSideParkingDTO parkingDTO) {
		Optional.ofNullable(channelTodoVO)
			.ifPresent(t -> saveParkingImages(t.getParkingId(), parkingDTO));
	}

	private void saveParkingImages(Long parkingOrderId, RoadSideParkingDTO parkingDTO) {
		if (Objects.isNull(parkingOrderId)) {
			return;
		}

		try {
			this.parkingImageRepository.saveOrUpdate(
				ParkingImageFactory.createByTypeValueMap(
					parkingOrderId,
					parkingDTO.getParklotId(),
					ParkingStatusEnum.resolve(parkingDTO.getParkingStatus()),
					parkingDTO.getImageUrlMap()
				)
			);
		} catch (Exception e) {
			log.warn("save parking images failed! ", e);
		}

	}

	/**
	 * 路内停车
	 * Transactional 保证释放锁时，事务提交
	 *
	 * @param req   请求体
	 * @param place 车位信息
	 * @return ChannelTodoVO 代办信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ChannelTodoVO roadSideParking(RoadSideParkingDTO req, ParkingPlace place) {
		log.info("路内停车 - 车位支付号[{}], 参数: {}", place.getPayCode(), req);

		// 过滤车位锁触发
		if (filterLockTrigger(req, place)) {
			log.info("过滤车场[{}]的车位锁触发", place.getParklotId());
			return null;
		}

		ChannelTodoVO channelTodoVO;
		if (req.isEnter()) {
			channelTodoVO = enter(req, place);
		} else {
			channelTodoVO = exit(req, place);
		}

		return channelTodoVO;
	}

	private ChannelTodoVO enter(RoadSideParkingDTO req, ParkingPlace place) {
		Vehicle vehicle = vehicleAssembler.fromDTO(req.getVehicleDTO());

		// 查询当前停车位是否存在车辆在场的停车订单
		PlaceParkingEventInfo parkingEventInfo = new PlaceParkingEventInfo(req.getPlaceId());
		parkingEventInfo.setVehicle(vehicle);
		parkingEventInfo.setDatetime(req.getDate());
		parkingEventInfo.setImageUrl(req.getImageUrl());
		parkingEventInfo.setImageCollection(ParkingImageAssembler.mapToParkingImageCollection(req.getImageUrlMap()));
		parkingEventInfo.setTriggerWay(ParkingTriggerWay.resolve(req.getTriggerType()));
		if (this.roadsideParkingRepeatEnterEventHandler.handle(parkingEventInfo)) {
			return null;
		}

		// 若{{oftenEnterExitGapTime}}分钟内多次进出场，则将前一条置为在场，新的进出抛弃
		ParkingOrder parkingOrder = mergeFrequentEnterExitOrder(req, place);
		if (parkingOrder != null) {
			return null;
		}

		// 设置车辆信息ID
		req.setVehicleId(vehicle.getId());
		// 执行进场处理，创建代办
		ChannelTodoVO resultTodo = createTodo(req, place);
		// 更新车位占用状态
		placeService.updateCurStatus(place.getId(), ParkingPlaceIdleState.OCCUPIED, resultTodo.getPlate());

		return resultTodo;
	}

	@Override
	public ParkingOrder mergeFrequentEnterExitOrder(RoadSideParkingDTO req, ParkingPlace place) {
		ParkingOrder parkingOrder = parkingOrderService.queryLastParkingOrderByPlace(place.getId());
		long oftenEnterExitGapTime = parkingRepository.getOftenEnterExitGapTime().toMinutes();

		if (parkingOrder != null && PlateValidator.isPlate(req.getPlate()) && parkingOrder.getPlate().equals(req.getPlate())
			&& ParkingStatusEnum.PARK_OUT.getValue() == parkingOrder.getParkingStatus()
			&& !req.getDate().after(DateUtil.plusMinutes(parkingOrder.getExitTime(), oftenEnterExitGapTime))) {

			log.info("enter {}分钟内多次进出场 车牌:{} 车位:{} 车场:{}", oftenEnterExitGapTime, parkingOrder.getPlate(),
				parkingOrder.getPlaceId(), parkingOrder.getParklotId());

			// 10s循环判断支付锁是否为空
			try {
				for (int i = 0; i < 10; i++) {
					if (!CacheUtils.exists(TEMP_PARKING_ORDER_PAY_LOCK + parkingOrder.getId())) {
						break;
					}
					Thread.sleep(1000);
				}
			} catch (Exception e) {
				log.info("enter 10s循环判断支付锁是否为空失败 error:", e);
			}

			// 支付锁为空，修改前一条停车记录
			if (!CacheUtils.exists(TEMP_PARKING_ORDER_PAY_LOCK + parkingOrder.getId())) {
				// 将出场更改为入场
				roadsideParkingAppService.changeExitToEnter(parkingOrder.getId());
				// 删除多余的未缴订单
				TempParkingUnpaidOrder unpaidOrder = unpaidOrderService.getOneByParkingId(parkingOrder.getId());
				if (unpaidOrder != null) {
					unpaidOrderService.deleteUnpaidOrder(unpaidOrder.getId());
				}
				// 删除商户订单
				orderService.deleteMerchantOrder(parkingOrder.getId());
				// 删除出场图片
				parkingImageRepository.deleteByParkingOrderId(parkingOrder.getId(), ParkingStatusEnum.PARK_OUT);
				return parkingOrder;
			}
		}
		return null;
	}

	private ChannelTodoVO exit(RoadSideParkingDTO req, ParkingPlace place) {
		// 获取出场车辆信息
		Vehicle exitVehicle = vehicleAssembler.fromDTO(req.getVehicleDTO());

		ChannelTodoVO exitTodo = createTodo(req, place);

		// 查询当前停车订单
		ParkingOrder curParkingOrder = parkingOrderService.getById(exitTodo.getParkingId());
		if (Objects.isNull(curParkingOrder)) {
			return exitTodo;
		}

		// 获取进场车辆信息
		Vehicle enterVehicle = vehicleDomainService.getById(
			curParkingOrder.getVehicleId(), () -> Vehicle.of(curParkingOrder.getPlate())
		);

		// 根据进出场车辆判断是否为正常出场订单
		boolean isNormalExitOrder = isNormalExitOrder(enterVehicle, exitVehicle);

		// 如果车牌不匹配，且为巡检车触发则丢弃本次出场
		if (!isNormalExitOrder && ChannelWay.WAY_12.getValue().equals(curParkingOrder.getEnterWay())) {
			log.info("巡检车触发进出场车牌不一致，丢弃本次出场");
			return null;
		}

		// 更新车位空闲状态
		placeService.updateCurStatus(place.getId(), ParkingPlaceIdleState.IDLE, exitTodo.getPlate());

		if (!channelTodoService.isPass(exitTodo.getStatus())) {
			// 如果车牌不匹配，但是通过手动修改了车牌的，按正常流程处理
			if (!isNormalExitOrder && (CompareUtil.compare(curParkingOrder.getEnterWay(), ChannelWay.WAY_8.getValue()) == 0
				|| CompareUtil.compare(curParkingOrder.getEnterWay(), ChannelWay.WAY_9.getValue()) == 0)) {
				isNormalExitOrder = true;
			}

			// 如果为正常订单，尝试使用无感支付
			if (isNormalExitOrder) {

				publishDelayedUnconsciousPayMessage(exitTodo, curParkingOrder);

				exitTodo.setReasonId((long) UnpaidOrderType.TYPE100.getValue());
			} else {
				if (this.parkingErrorAppService.process(curParkingOrder)) {
					return null;
				}
				curParkingOrder.setMemo(
					"异常订单，异常原因：进出场车辆信息不一致，" +
						"进场车辆[" + enterVehicle.getPlate() + "," + enterVehicle.getPlateColor().chineseName() + "]," +
						"出场车辆[" + exitVehicle.getPlate() + "," + exitVehicle.getPlateColor().chineseName() + "]"
				);
				exitTodo.setReasonId((long) UnpaidOrderType.TYPE101.getValue());
			}

			exitTodo.setErrorAmount(exitTodo.getReceiveAmount());
			exitTodo.setErrorAmountType(2);
			if (exitTodo.getReceiveAmount().compareTo(BigDecimal.ZERO) > 0 && isNormalExitOrder) {
				// 生成未缴订单
				parkingOrderService.createUnpaidOrder(exitTodo, curParkingOrder);
			}
			exitTodo.setStatus(1);
			// 更新代办
			channelTodoService.updateById(exitTodo);
			// 更新出场
			parkingOrderService.updateParkingInfoWithExitedEvent(curParkingOrder, exitTodo);
			// 发送异常订单消息
			sender.sendMessage(JsonUtil.toJson(curParkingOrder), MessageConstant.LECENT_PARK_ORDER_ABNORMAL_KEY, Exchanges.LECENT_PARK_EXCHANGE_NAME, 10000);
		}

		return exitTodo;
	}

	/**
	 * 发布延迟无感支付消息
	 *
	 * @param resultTodo      代办信息
	 * @param curParkingOrder 当前停车记录
	 */
	private void publishDelayedUnconsciousPayMessage(ChannelTodoVO resultTodo, ParkingOrder curParkingOrder) {
		// 仅有牌车发无感支付消息
		if (!PlateValidator.isPlate(resultTodo.getPlate())) {
			return;
		}

		ParklotPayMerchantManager merchantManager = parklotPayMerchantRepository.getMerchantManager(resultTodo.getParklotId());
		if (!merchantManager.hasUnconsciousMerchant()) {
			return;
		}
		// 获取频繁进出场间隔时间，再加上60s，防止临界时间
		long delayTime = parkingRepository.getOftenEnterExitGapTime().toMillis() + 60000;
		// 发送延迟队列
		sender.sendDelayMessage(Func.toJson(resultTodo), delayTime, Exchanges.LECENT_PARK_DELAY_QUEUE_EXCHANGE,
			MessageConstant.LECENT_PARK_NO_TOUCH_PAY_KEY);
		// 缓存当前待办id
		CacheUtils.setEx(ParkCacheNames.PARKING_NO_TOUCH_PAY_TODO + curParkingOrder.getId(),
			resultTodo.getId().toString(), Duration.ofHours(1));
	}

	private boolean isNormalExitOrder(Vehicle enterVehicle, Vehicle exitVehicle) {
		// 排除出场原始车牌为空（目前非摄像头推送的车牌，原始车牌都为空），标记为正常
		if (exitVehicle.isEmptyOfOriginalPlate()) {
			return true;
		}
		// 排除进场车牌是无牌车的，标记为正常
		if (enterVehicle.isNoPlate()) {
			return true;
		}
		// 进出场车辆相同，标记为正常
		return enterVehicle.equals(exitVehicle);
	}

	private ChannelTodoVO createTodo(RoadSideParkingDTO req, ParkingPlace place) {
		Channel channel = ParkLotCaches.getChannelByNo(place.getParklotId(), req.isEnter() ? 0 : 1);
		LecentAssert.notNull(channel, "通道不存在！");

		Parklot parklot = ParkLotCaches.existParkLot(channel.getParklotId());
		LecentAssert.isTrue(parklot.isRoadSide(), "非路边停车不予进出场");

		ChannelTodo todo = buildRoadSideTodo(req, parklot, channel);
		todo.setPlaceId(place.getId());

		if (Func.isBlank(todo.getPlate())) {
			todo.setPlate(Vehicle.NO_PLATE);
		}

		return this.channelTodoService.createTodo(todo, channel, parklot);
	}

	/**
	 * 根据停车记录创建代办
	 * 创建代办
	 *
	 * @param parkingOrder 停车记录
	 * @return {@link ChannelTodo}
	 */
	@Override
	public ChannelTodo todoParkingDetail(ParkingOrder parkingOrder) {
		//创建待办
		ParkTodoContext context = this.earlyPayTodo(parkingOrder);
		//保存待办
		ChannelTodo resultTodo = context.getChannelTodo();
		resultTodo.setCreateWay(CreateWay.INSIDE.getValue());
		channelTodoService.updateById(resultTodo);
		return resultTodo;
	}

	@Deprecated
	@Override
	public ParkingDetail todoParkingDetail(ParkingPlace place, Long parkingId) {
		// 1. 构建基础停车详情
		ParkingDetail detail = buildBaseParkingDetail(place, parkingId);

		// 2. 设置车位相关信息
		detail.setPlaceCode(place.getPlaceCode());
		detail.setPayCode(place.getPayCode());
		detail.setPlaceId(place.getId());
		detail.setParkingId(place.getParklotId());

		// 3. 设置设备组合和商户信息
		detail.setDeviceComboList(this.channelTodoService.deviceCombo(place.getId()));
		this.channelTodoService.addMerchantField(detail);

		// 4. 设置未支付订单历史
		setUnpaidOrderHistory(detail, parkingId);

		return detail;
	}

	@Override
	public ParkingDetail todoParkingDetailV2(ParkingPlace place, Long parkingId) {
		// 1. 构建基础停车详情
		ParkingDetail detail = buildBaseParkingDetail(place, parkingId);

		// 2. 设置车位相关信息
		detail.setPlaceCode(place.getPlaceCode());
		detail.setPayCode(place.getPayCode());
		detail.setPlaceId(place.getId());
		detail.setParkingId(place.getParklotId());

		// 3. 设置设备组合和商户信息
		detail.setDeviceComboList(this.channelTodoService.deviceCombo(place.getId()));
		this.channelTodoService.addMerchantField(detail);

		// 4. 设置未支付订单历史
		setUnpaidOrderHistoryV2(detail, parkingId);

		return detail;
	}

	/**
	 * 构建基础停车详情
	 *
	 * @param place     车位信息
	 * @param parkingId 停车记录ID
	 * @return 停车详情
	 */
	private ParkingDetail buildBaseParkingDetail(ParkingPlace place, Long parkingId) {
		ParkingOrder parkingOrder = parkingOrderService.getById(parkingId);

		// 已结束的停车记录
		if (parkingOrder != null && ParkingStatusEnum.isExited(parkingOrder.getParkingStatus())) {
			return buildDetailForFinishedParking(parkingOrder);
		}

		// 未找到停车记录
		if (parkingId != null && parkingOrder == null) {
			ParkingDetail detail = new ParkingDetail();
			detail.setStatus(0);
			return detail;
		}

		// 在场停车记录或无停车记录
		return buildDetailForOngoingParking(place, parkingOrder);
	}

	/**
	 * 为已结束的停车记录构建详情
	 *
	 * @param parkingOrder 停车记录
	 * @return 停车详情
	 */
	private ParkingDetail buildDetailForFinishedParking(ParkingOrder parkingOrder) {
		ParkingOrderDTO orderDTO = new ParkingOrderDTO();
		orderDTO.setId(parkingOrder.getId());
		ParkingDetail detail = parkingOrderService.queryParkingDetail(orderDTO);
		if (detail == null) {
			detail = new ParkingDetail();
			// 未查询到停车记录
			detail.setStatus(0);
		} else if (detail.getReceiveAmount().compareTo(BigDecimal.ZERO) <= 0) {
			// 免费离场
			detail.setStatus(2);
		} else {
			// 需要缴费
			detail.setStatus(1);
		}
		// 设置异常原因
		detail.setErrorReason(BigDecimal.ZERO.compareTo(detail.getReceiveAmount()) >= 0 ? "" : UnpaidOrderType.getNameByValue(Func.toInt(parkingOrder.getReasonIds())));
		// 获取计费规则
		Parklot parklot = ParkLotCaches.getParkLot(detail.getParklotId());
		detail.setChargeRuleMap(tempParkingChargeRuleService.chargeRuleList(parklot.getId()));
		return detail;
	}

	/**
	 * 为在场停车记录或无停车记录构建详情
	 *
	 * @param place        车位信息
	 * @param parkingOrder 停车记录
	 * @return 停车详情
	 */
	private ParkingDetail buildDetailForOngoingParking(ParkingPlace place, ParkingOrder parkingOrder) {
		ChannelTodo todo = buildRoadSideTodo(new RoadSideParkingDTO());
		todo.setPlaceId(place.getId());
		todo.setPlate(place.getPlaceCode());
		todo.setParklotId(place.getParklotId());
		todo.setTriggerType(ChannelWay.WAY_2.getValue());

		ParkTodoContext context = earlyPayTodo(todo, parkingOrder);
		return convertParkingDetail(context);
	}

	/**
	 * 设置未支付订单历史
	 *
	 * @param detail    停车详情
	 * @param parkingId 当前停车记录ID
	 */
	private void setUnpaidOrderHistory(ParkingDetail detail, Long parkingId) {
		String plate = detail.getPlate();
		if (Func.isBlank(plate) || !PlateCheckUtils.isPlate(plate)) {
			return;
		}

		List<ParkingDetail> unpaidOrders = parkingOrderService.userUnpaidOrder(plate, (Long) null);
		if (Func.isEmpty(unpaidOrders)) {
			return;
		}

		// 为所有未支付订单添加商户信息
		unpaidOrders.forEach(this.channelTodoService::addMerchantField);

		// 获取当前商户ID
		String merchantId = Optional.ofNullable(detail.getMerchantId())
			.orElseGet(() -> unpaidOrders.get(0).getMerchantId());

		// 过滤出相同商户的历史未支付订单
		List<ParkingDetail> sameMerchantOrders = unpaidOrders.stream()
			.filter(order -> merchantId.equals(order.getMerchantId())
				&& plate.equals(order.getPlate())
				&& !order.getParkingId().equals(parkingId))
			.collect(Collectors.toList());

		detail.setHistoryUnpaidOrderList(sameMerchantOrders);
	}

	/**
	 * 设置未支付订单历史
	 *
	 * @param detail    停车详情
	 * @param parkingId 当前停车记录ID
	 */
	private void setUnpaidOrderHistoryV2(ParkingDetail detail, Long parkingId) {
		String plate = detail.getPlate();
		if (Func.isBlank(plate) || !PlateCheckUtils.isPlate(plate)) {
			return;
		}

		List<ParkingDetail> unpaidOrders = parkingOrderService.userUnpaidOrder(plate, detail.getMerchantId());
		if (Func.isEmpty(unpaidOrders)) {
			return;
		}

		// 过滤掉当前停车记录
		List<ParkingDetail> filteredOrders = unpaidOrders.stream()
			.filter(order -> !order.getParkingId().equals(parkingId))
			.collect(Collectors.toList());

		detail.setHistoryUnpaidOrderList(filteredOrders);
	}

	private ParkingDetail convertParkingDetail(ParkTodoContext context) {
		ChannelTodo resultTodo = context.getChannelTodo();
		log.info("convertParkingDetail resultTodo={}", Func.toJson(resultTodo));
		resultTodo.setCreateWay(CreateWay.INSIDE.getValue());
		channelTodoService.updateById(resultTodo);

		Parklot parklot = context.getParkLot();
		ParkingDetail detail = Func.copy(resultTodo, ParkingDetail.class);
		if (detail != null) {
			detail.setTodoId(resultTodo.getId());
			detail.setParklotName(parklot.getName());
			detail.setNoRecord(PassTemplate.NO_ENTER_RECORD.name().equals(resultTodo.getType()));
			detail.setParklotType(parklot.getParklotType());
			detail.setChargeRuleMap(tempParkingChargeRuleService.chargeRuleList(parklot.getId()));
			detail.setParklotId(parklot.getId());
			detail.setTypes(PassTemplate.getMsg(resultTodo.getType()));
			detail.setEnterImgUrl(resultTodo.getEnterImageUrl());
			detail.setExitTime(resultTodo.getDate());
			detail.setExitImgUrl(resultTodo.getImageUrl());
			detail.setParkingStatus(ParkingStatusEnum.PARK_IN.getValue());
			// 设置车辆属性
			List<PlateProperty> properties = platePropertyService.queryPlateProperties(detail.getParklotId(),
				detail.getPlate(), detail.getEnterTime(), Optional.ofNullable(detail.getExitTime()).orElse(new Date()));
			PlateProperty plateProperty = properties.stream().max(Comparator.comparing(PlateProperty::getEndTime)).orElse(null);
			detail.setPlateProperty(plateProperty);
			// 设置车牌颜色
			if (detail.getVehicleId() != null) {
				Vehicle vehicle = vehicleDomainService.getById(detail.getVehicleId());
				if (null != vehicle) {
					detail.setVehicleDO(VehicleConverter.toDO(vehicle));
					detail.setPlateColor(vehicle.getPlateColor().code());
				}
			}
			//  设置待办状态
			convertStatus(detail, resultTodo, context.getParkingOrder() != null);
		}

		return detail;
	}

	private void convertStatus(ParkingDetail detail, ChannelTodo resultTodo, boolean haveParkingOrder) {
		//
		if (resultTodo.getStatus() != 0 && resultTodo.getReceiveAmount().compareTo(BigDecimal.ZERO) <= 0) {
			if (haveParkingOrder) {
				// 免费离场
				detail.setStatus(2);
			} else {
				// 无进场记录
				detail.setStatus(0);
			}
		} else {
			if (TypeEnum.NO_ENTER_RECORD.getName().equals(resultTodo.getType())) {
				//无进场记录直接进入订单列表页
				detail.setStatus(0);
			} else {
				detail.setStatus(1);
				if (resultTodo.getStatus() == 2) {
					//已缴费 免费放行 支付金额为0
					detail.setReceiveAmount(BigDecimal.ZERO);
				}
			}


		}
	}

	private ChannelTodo buildRoadSideTodo(RoadSideParkingDTO req, Parklot parklot, Channel channel) {
		ChannelTodo todo = buildRoadSideTodo(req);
		this.channelTodoService.copyTodo(parklot, channel, todo);
		return todo;
	}

	private ChannelTodo buildRoadSideTodo(RoadSideParkingDTO req) {
		ChannelTodo todo = new ChannelTodo();
		Date date = req.getDate() != null ? req.getDate() : new Date();
		todo.setPlate(req.getPlate());
		todo.setStatus(ChannelConstant.TODO_STATUS_AUTO_PASS);
		todo.setImageUrl(req.getImageUrl());
		todo.setEnterImageUrl(req.getImageUrl());
		todo.setTriggerType(req.getTriggerType());
		todo.setChargeType(ChargeEnum.TEMP_STOP.name());
		todo.setDate(date);
		todo.setEnterTime(date);
		todo.setOccupied(false);
		todo.setIsMqOpenGate(false);
		todo.setVehicleId(req.getVehicleId());
		return todo;
	}

	//订单车牌处理
	private ParkTodoContext earlyPayTodo(ChannelTodo todo, ParkingOrder parkingOrder) {
		ParkTodoContext context = ParkTodoContext.builder(new ParkChannelMessageEvent(todo));
		Optional.ofNullable(parkingOrder).ifPresent(p -> context.setParkingOrder(parkingOrder));
		ParkingOrder order = context.getParkingOrder();
		//订单不为空  车牌符合规则
		if (order != null && PlateCheckUtils.isPlate(order.getPlate())) {
			//订单设置车牌
			context.setPlate(order.getPlate());
		}
		//创建订单
		parkAccessClient.createPayTodo(context);
		return context;
	}

	private ParkTodoContext earlyPayTodo(ParkingOrder parkingOrder) {
		ChannelTodo todo = buildRoadSideTodo(new RoadSideParkingDTO());
		todo.setPlaceId(parkingOrder.getId());
		todo.setPlate(parkingOrder.getPlate());
		todo.setParklotId(parkingOrder.getParklotId());
		todo.setTriggerType(ChannelWay.WAY_2.getValue());
		todo.setDate(parkingOrder.getExitTime());
		todo.setImageUrl(parkingOrder.getExitImageUrl());
		ParkTodoContext context = ParkTodoContext.builder(new ParkChannelMessageEvent(todo));
		context.setParkingOrder(parkingOrder);
		if (PlateCheckUtils.isPlate(parkingOrder.getPlate())) {
			//订单设置车牌
			context.setPlate(parkingOrder.getPlate());
		}
		//创建订单
		parkAccessClient.createPayTodo(context);
		return context;
	}

	@Override
	public ChannelTodo todoParkingDetail(Long placeId, String plate, Long parklotId) {
		ParkingDetail parkingDetail = new ParkingDetail();
		parkingDetail.setPlaceId(placeId);
		parkingDetail.setPlaceCode(plate);
		parkingDetail.setParklotId(parklotId);
		return parkingOrderService.todoParkingDetail(parkingDetail);
	}

	@Override
	public List<ParklotDeviceRetVO> findParklotDeviceInfoByPlaceId(Long placeId) {
		return parklotDeviceRetService.findParklotDeviceInfoByPlaceId(placeId);
	}

	@Override
	public PlateProperty getPlateProperty(Long parklotId, String plate, Date enterTime, Date exitTime) {
		List<PlateProperty> properties = platePropertyService.queryPlateProperties(parklotId, plate, enterTime,
			Optional.ofNullable(exitTime).orElse(new Date()));
		return properties.stream().max(Comparator.comparing(PlateProperty::getEndTime)).orElse(null);
	}
}
