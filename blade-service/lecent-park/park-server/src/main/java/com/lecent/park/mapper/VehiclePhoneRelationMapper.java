package com.lecent.park.mapper;

import com.lecent.park.entity.BVehiclePhoneRelation;
import com.lecent.park.vo.vehiclePhoneRelation.CSCBVehiclePhoneRelationVO;
import com.lecent.park.vo.vehiclePhoneRelation.VehiclePhoneRelationQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车牌手机号采集表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface VehiclePhoneRelationMapper extends BaseMapper<BVehiclePhoneRelation> {

	List<CSCBVehiclePhoneRelationVO> bVehiclePhoneRelationPage(IPage page, VehiclePhoneRelationQueryVO queryVO);

	List<CSCBVehiclePhoneRelationVO> bVehiclePhoneRelation(@Param("queryVO") VehiclePhoneRelationQueryVO queryVO);

	/**
	 * 获取车辆最新手机号
	 *
	 * @param vehicleId 车辆id
	 * @return 手机号
	 */
	String getLastPhoneByVehicleId(@Param("vehicleId") Long vehicleId);
}
