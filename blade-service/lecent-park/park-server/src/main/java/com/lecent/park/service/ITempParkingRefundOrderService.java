package com.lecent.park.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lecent.park.en.temporder.RefundStatus;
import com.lecent.park.entity.TempParkingRefundOrder;
import com.lecent.park.vo.TempParkingRefundOrderVO;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 退款订单 服务类
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
public interface ITempParkingRefundOrderService extends BaseService<TempParkingRefundOrder> {

    /**
     * 自定义分页
     *
     * @param page                   页面
     * @param tempParkingRefundOrder 临时停车退款订单
     * @return {@link IPage}<{@link TempParkingRefundOrderVO}>
     */
    IPage<TempParkingRefundOrderVO> selectTempParkingRefundOrderPage(IPage<TempParkingRefundOrderVO> page, TempParkingRefundOrderVO tempParkingRefundOrder);

    /**
     * 订单详情
     *
     * @param tempParkingRefundOrder 临时停车退款订单
     * @return {@link TempParkingRefundOrderVO}
     */
    TempParkingRefundOrderVO orderDetail(TempParkingRefundOrderVO tempParkingRefundOrder);

    /**
     * 创建退款订单
     *
     * @param tempParkingRefundOrder 临时停车退款订单
     * @return {@link Boolean}
     */
    TempParkingRefundOrder createRefundOrder(TempParkingRefundOrderVO tempParkingRefundOrder);

    /**
     * 退款
     *
     * @param tempParkingRefundOrder 退款订单
     * @return {@link Boolean}
     */
    Boolean refund(TempParkingRefundOrderVO tempParkingRefundOrder);

    /**
     * 导出
     *
     * @param response               响应
     * @param tempParkingRefundOrder 退款订单
     */
    void export(HttpServletResponse response, TempParkingRefundOrderVO tempParkingRefundOrder);

    /**
     * 查询退款金额
     *
     * @param orderIds 订单id
     * @return {@link BigDecimal}
     */
    BigDecimal queryRefundAmount(List<Long> orderIds);

    /**
     * 查询退款金额
     *
     * @param orderId 订单id
     * @return {@link BigDecimal}
     */
    BigDecimal queryRefundAmount(Long orderId);

    /**
     * 更新状态
     *
     * @param ids          id
     * @param refundStatus 退款状态
     * @return {@link Boolean}
     */
    Boolean updateStatus(List<Long> ids, RefundStatus refundStatus);
}
