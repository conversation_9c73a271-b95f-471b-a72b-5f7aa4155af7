package com.leliven.park.adapter.web.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.leliven.park.application.basic.dto.CameraCaptureRecordDTO;
import com.leliven.park.application.basic.dto.CameraCaptureRecordGroupDTO;
import com.leliven.park.application.basic.service.ParkingPlaceEventLogAppServiceI;
import com.leliven.park.application.basic.service.ParkingPlaceQueryAppServiceI;
import com.leliven.park.common.model.valueobject.BasicParkingScene;
import com.leliven.park.domain.parking.core.model.objectvalue.RoadsideParkingLockCtrlSceneParam;
import com.leliven.park.domain.parking.core.support.RoadsideParkingLockCtrlProcessor;
import com.leliven.park.infrastructure.gateway.persistence.basic.mysql.dataobject.ParkingPlaceEventLogDO;
import com.leliven.park.infrastructure.gateway.persistence.basic.query.ParkingPlaceCameraCaptureRecordQuery;
import com.leliven.park.infrastructure.gateway.persistence.basic.query.ParkingPlaceEventLogQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 车位控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/parking-place")
@Api(value = "车位信息", tags = "车位信息接口")
public class ParkingSpaceController extends BladeController {

	private final RoadsideParkingLockCtrlProcessor lockCtrlProcessor;
	private final ParkingPlaceQueryAppServiceI parkingPlaceQueryAppService;
	private final ParkingPlaceEventLogAppServiceI eventLogAppService;

	/**
	 * 查询车位摄像头抓拍记录(分页)
	 */
	@GetMapping("/{pid}/camera-capture-records/page")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "查询车位摄像头抓拍记录", notes = "分页查询")
	public R<IPage<CameraCaptureRecordDTO>> selectCameraCaptureRecordPage(@PathVariable("pid") Long parkingPlaceId,
																		  Query pageQuery,
																		  @Validated ParkingPlaceCameraCaptureRecordQuery query) {
		return R.data(this.parkingPlaceQueryAppService.selectCameraCaptureRecordPage(pageQuery, parkingPlaceId, query));
	}

	/**
	 * 查询车位事件日志(分页)
	 */
	@GetMapping("/{pid}/event-log/page")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "查询车位事件日志", notes = "分页查询")
	public R<IPage<ParkingPlaceEventLogDO>> selectCameraCaptureRecordPage(@PathVariable("pid") Long parkingPlaceId,
																		  @Validated ParkingPlaceEventLogQuery query) {
		query.setParkingPlaceId(parkingPlaceId);
		return R.data(this.eventLogAppService.selectEventLogPage(query));
	}

	@PostMapping("/{pid}/lock")
    @ApiOperation(value = "车位升锁")
    public R<Boolean> lock(@PathVariable("pid") Long placeId) {
        lockCtrlProcessor.lock(new RoadsideParkingLockCtrlSceneParam(placeId, BasicParkingScene.MANUAL_OPT));
        return R.status(true);
    }

	@PostMapping("/{pid}/unlock")
    @ApiOperation(value = "车位降锁")
    public R<Boolean> unlock(@PathVariable("pid") Long placeId) {
        lockCtrlProcessor.unlock(new RoadsideParkingLockCtrlSceneParam(placeId, BasicParkingScene.MANUAL_OPT));
        return R.status(true);
    }

	/**
	 * PDA端查询车位摄像头抓拍记录(分页)
	 */
	@GetMapping("/{pid}/pda/camera-capture-records/page")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "查询车位摄像头抓拍记录", notes = "分页查询")
	public R<IPage<CameraCaptureRecordGroupDTO>> selectPdaCameraCaptureRecordPage(@PathVariable("pid") Long parkingPlaceId,
																				  Query pageQuery,
																				  @Validated ParkingPlaceCameraCaptureRecordQuery query) {
		return R.data(this.parkingPlaceQueryAppService.selectPdaCameraCaptureRecordPage(pageQuery, parkingPlaceId, query));
	}

}
