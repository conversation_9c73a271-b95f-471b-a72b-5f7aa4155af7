package org.springblade.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 键值对实体类
 * <AUTHOR>
 * @description: TODO
 * @date 2021/8/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeyValueVo implements Serializable {

	/**
	 * id
	 */
	private String id;
	/**
	 * 编码
	 */
	private String key;
	/**
	 * 名称
	 */
	private String value;

	public KeyValueVo(String key, String value) {
		this.key = key;
		this.value = value;
	}
}
