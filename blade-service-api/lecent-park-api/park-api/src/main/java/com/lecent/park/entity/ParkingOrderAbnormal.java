package com.lecent.park.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023/12/7 14:56
 */
@Data
@TableName("c_parking_order_abnormal")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "异常停车记录处理表", description = "ParkingOrderAbnormal对象")
public class ParkingOrderAbnormal extends TenantEntity {

	@ApiModelProperty(value = "车位ID")
	private Long placeId;

	@ApiModelProperty(value = "停车场ID")
	private Long parklotId;

	@ApiModelProperty(value = "停车记录id")
	private Long parkingId;

	@ApiModelProperty(value = "异常类型")
	private Integer abnormalType;

	@ApiModelProperty(value = "处理人")
	private Long handlerPersonId;

	@ApiModelProperty(value = "处理人")
	private String handlerPersonName;

	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "处理时间")
	private Date handlerDate;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "原因")
	private String  reason;

	@ApiModelProperty(value = "是否转工单 0 否 1是")
	private Integer isWorkOrder;

	@ApiModelProperty(value = "工单编号")
	private String workOrderCode;

	@ApiModelProperty(value = "欠缴")
	private BigDecimal unPaidAmount;

	@ApiModelProperty(value = "实收")
	private BigDecimal paidAmount;

	@ApiModelProperty(value = "应收")
	private BigDecimal totalAmount;

	@ApiModelProperty(value = "附件")
	private String attachment;
}
