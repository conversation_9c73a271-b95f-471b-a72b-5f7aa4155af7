package com.lecent.park.dto;

import com.lecent.park.entity.ParkingOrderAbnormal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024/1/4 9:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("数据传输对象")
public class ParkingOrderAbnormalDTO extends ParkingOrderAbnormal {

	/**
	 * 车场id
	 */
	private List<Long> parkLotIds;

	private String parkLotIdStr;

	private List<String> tenantIds;

	/**
	 * 处理人
	 */
	private Long handlerPersonId;

	/**
	 * 进场开始时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "进场开始时间")
	private Date enterStartTime;
	/**
	 * 进场结束时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "进场结束时间")
	private Date  enterEndTime;

	/**
	 * 处理时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "处理开始时间")
	private Date handlerDateStart;
	/**
	 * 处理时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "处理结束时间")
	private Date  handlerDateEnd;


	private Integer abnormalType;



	/**
	 * 地区代码
	 */
	private String regionCode;

	/**
	 * 车牌号
	 */
	@ApiModelProperty(value = "车牌号")
	private String plate;

	@ApiModelProperty(value = "车场类型（1-路外2-路内）")
	private Integer parklotType;

	@ApiModelProperty(value = "车位SN码（支付号）")
	private String payCode;

	@ApiModelProperty(value = "车位号")
	private String placeCode;


	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "出场开始时间")
	private Date exitStartTime;

	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty(value = "出场结束时间")
	private Date exitEndTime;

	private Long placeId;
	private String placeIds;

	private List<Long> placeIdList;


	/**
	 * 分组类型  1 差异类型  2 处理人  3 处理时间分组  4 进场时间分组
	 */
	private Integer groupByType;

	/**
	 * 日期类型 1 日 3 月 4 年  5 自定义
	 */
	private Integer dateType;

	private Long workOrderConfigId;

	/**
	 * 工单标题
	 */
	private String  workOrderTitle;
	/**
	 * 工单附件
	 */
	private  String workOrderAnnex;

	private Integer handlerStatus;



	public List<Long> mergePlaceIdList(){
		List<Long> ids=null;
		if(Func.isNotBlank(this.placeIds)){
			ids=Func.toLongList(this.placeIds);
		}
		return ids;
	}

	public List<Long> mergeParkLotId(){
		List<Long> parkLotIds=null;
		if(Func.isNotBlank(this.parkLotIdStr)){
			parkLotIds=Func.toLongList(this.parkLotIdStr);
		}else if(this.getParklotId() !=null){
			parkLotIds = new ArrayList<>();
			parkLotIds.add(this.getParklotId());
		}
		return parkLotIds;
	}
}
