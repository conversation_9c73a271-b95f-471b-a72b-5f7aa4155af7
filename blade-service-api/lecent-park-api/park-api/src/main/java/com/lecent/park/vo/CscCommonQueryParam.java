package com.lecent.park.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.Func;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "通用查询参数")
public class CscCommonQueryParam extends Query implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("区域编码")
	private String regionCode;

	@ApiModelProperty("租户id")
	private String tenantIds;

	private String tenantId;
	@ApiModelProperty("车场id")
	private Long parklotId;

	@ApiModelProperty("车场id")
	private List<Long> parklotIds;

	@ApiModelProperty("车场id")
	private List<Long> ids;

	@ApiModelProperty("车场名称")
	private String parklotName;

	@ApiModelProperty("设备类型")
	private String deviceType;

	@ApiModelProperty("告警类型")
	private Integer alarmType;

	@ApiModelProperty("告警类别")
	private String alarmClass;

	@ApiModelProperty(value = "当前等级")
	private Integer level;

	@ApiModelProperty("车牌")
	private String plate;

	@ApiModelProperty(value = "订单号")
	private String tradeNo;

	@ApiModelProperty("支付码")
	private String payCode;

	@ApiModelProperty("车位号")
	private String placeCode;

	@ApiModelProperty(value = "进场开始时间")
	private String enterStartTime;

	@ApiModelProperty(value = "进场结束时间")
	private String enterEndTime;

	@ApiModelProperty(value = "出场开始时间")
	private String exitStartTime;

	@ApiModelProperty(value = "出场结束时间")
	private String exitEndTime;

	@ApiModelProperty(value = "支付起始时间")
	private String payStartTime;

	@ApiModelProperty(value = "支付结束时间")
	private String payEndTime;

	@ApiModelProperty(value = "是否今天")
	private Integer isCurrToday;

	@ApiModelProperty(value = "告警状态")
	private List<Integer> statusList;

	@ApiModelProperty(value = "商户ID")
	private String merchantId;
	@ApiModelProperty(value = "商户ID 集合")
	private List<String> merchantIds;

	@ApiModelProperty(value = "最小金额")
	private BigDecimal minAmount;

	@ApiModelProperty(value = "最大金额")
	private BigDecimal maxAmount;

	@ApiModelProperty(value = "是否地锁设备 1是")
	private Integer isGroundLock;

	public List<String> getTenantIds() {
		return Func.toStrList(tenantIds);
	}

	public String getRegionCode() {
		if (Func.isNotBlank(regionCode) && regionCode.length() == 6) {
			if (regionCode.endsWith("0000")) {
				return regionCode.substring(0, 2);
			} else if (regionCode.endsWith("00")) {
				return regionCode.substring(0, 4);
			}
		}
		return regionCode;
	}
}
